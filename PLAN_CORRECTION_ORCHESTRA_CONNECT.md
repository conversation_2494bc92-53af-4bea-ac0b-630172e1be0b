# 🎯 PLAN DE CORRECTION ORCHESTRA CONNECT

## 🔍 DIAGNOSTIC COMPLET

### ✅ **CE QUI FONCTIONNE**
- ✅ Outil 'ask' présent et identique à Suna
- ✅ Tous les outils techniques identiques (web_search_tool.py, etc.)
- ✅ Intégration Firecrawl parfaite
- ✅ Configuration MCP identique
- ✅ Architecture générale correcte

### 🚨 **PROBLÈMES IDENTIFIÉS**

#### **1. PROBLÈME PRINCIPAL : OUTILS DÉSACTIVÉS PAR DÉFAUT**
```typescript
// Dans tools.ts - TOUS les outils sont disabled
'web_search_tool': { enabled: false, ... }
'sb_files_tool': { enabled: false, ... }
'sb_browser_tool': { enabled: false, ... }
```

#### **2. LOGIQUE CONDITIONNELLE DANS run.py**
```python
if enabled_tools is None:
    # Aucun agent → TOUS les outils activés (mode Sun<PERSON>)
    thread_manager.add_tool(SandboxWebSearchTool, ...)
else:
    # Agent personnalisé → Seuls les outils enabled=true
    if enabled_tools.get('web_search_tool', {}).get('enabled', False):
        thread_manager.add_tool(SandboxWebSearchTool, ...)
```

#### **3. AGENTS ORCHESTRA CONNECT CRÉÉS SANS OUTILS**
Les agents personnalisés héritent de DEFAULT_AGENTPRESS_TOOLS où tout est `enabled: false`

## 🛠️ SOLUTIONS RECOMMANDÉES

### **SOLUTION 1 : CRÉER UN PROFIL ORCHESTRA CONNECT OPTIMISÉ**

#### **A. Modifier tools.ts pour Orchestra Connect**
```typescript
export const ORCHESTRA_CONNECT_TOOLS: Record<string, { enabled: boolean; description: string; icon: string; color: string }> = {
    'sb_shell_tool': { enabled: true, ... },
    'sb_files_tool': { enabled: true, ... },
    'sb_browser_tool': { enabled: true, ... },
    'web_search_tool': { enabled: true, ... }, // CRITIQUE pour Firecrawl
    'sb_vision_tool': { enabled: true, ... },
    'sb_deploy_tool': { enabled: false, ... }, // Optionnel
    'sb_expose_tool': { enabled: false, ... }, // Optionnel
    'data_providers_tool': { enabled: false, ... }, // Optionnel
};
```

#### **B. Créer un template Orchestra Connect**
```typescript
const ORCHESTRA_CONNECT_TEMPLATE = {
  name: 'Alex - Orchestra Connect',
  description: 'Assistant IA polyvalent et souriant d\'Orchestra Connect',
  system_prompt: `You are Alex, votre employé IA d'Orchestra Connect. 
  Vous êtes souriant, sympa et professionnel. Vous avez accès à tous les outils 
  nécessaires pour aider efficacement les utilisateurs.`,
  agentpress_tools: ORCHESTRA_CONNECT_TOOLS,
  avatar: '🎭',
  avatar_color: '#4F46E5'
};
```

### **SOLUTION 2 : CORRIGER LE PROMPT ORCHESTRA CONNECT**

#### **A. Simplifier le prompt (garder l'essentiel)**
```python
# Garder seulement :
- Identité Alex + Orchestra Connect (concise)
- Instruction français
- Personnalité sympa et souriante
- Capacités identiques à Suna
- Règles d'attachments simplifiées
```

#### **B. Réduire les références à l'outil 'ask'**
Aligner sur le niveau Suna pour éviter la sur-utilisation

### **SOLUTION 3 : CONFIGURATION MCP OPTIMISÉE**

#### **A. MCP par défaut pour Orchestra Connect**
```json
{
  "configured_mcps": [
    {
      "name": "web-search",
      "qualifiedName": "@modelcontextprotocol/server-web-search",
      "config": {},
      "enabledTools": ["web_search", "scrape_webpage"]
    }
  ]
}
```

### **SOLUTION 4 : CRÉER UN AGENT ORCHESTRA CONNECT PARFAIT**

#### **Configuration complète recommandée :**
```json
{
  "name": "Alex - Orchestra Connect",
  "description": "Assistant IA souriant et efficace d'Orchestra Connect",
  "system_prompt": "PROMPT_OPTIMISÉ_ALEX",
  "agentpress_tools": {
    "sb_shell_tool": {"enabled": true, "description": "Terminal operations"},
    "sb_files_tool": {"enabled": true, "description": "File management"},
    "sb_browser_tool": {"enabled": true, "description": "Browser automation"},
    "web_search_tool": {"enabled": true, "description": "Web search with Firecrawl"},
    "sb_vision_tool": {"enabled": true, "description": "Image processing"},
    "sb_deploy_tool": {"enabled": false, "description": "Deployment"},
    "sb_expose_tool": {"enabled": false, "description": "Port exposure"},
    "data_providers_tool": {"enabled": false, "description": "External APIs"}
  },
  "configured_mcps": [],
  "custom_mcps": [],
  "avatar": "🎭",
  "avatar_color": "#4F46E5",
  "is_default": false
}
```

## 📋 PLAN D'IMPLÉMENTATION

### **PHASE 1 : CORRECTIONS IMMÉDIATES**
1. ✅ Créer ORCHESTRA_CONNECT_TOOLS dans tools.ts
2. ✅ Créer un prompt Alex optimisé (basé sur Suna)
3. ✅ Créer un template d'agent Orchestra Connect

### **PHASE 2 : CRÉATION AGENT PARFAIT**
1. ✅ Utiliser l'API pour créer l'agent avec la bonne config
2. ✅ Tester tous les outils (web_search, ask, files, etc.)
3. ✅ Valider Firecrawl et scraping

### **PHASE 3 : TESTS ET VALIDATION**
1. ✅ Reproduire la demande Hyundai Tucson
2. ✅ Vérifier que les vrais liens sont extraits
3. ✅ Confirmer personnalité Alex souriante
4. ✅ Valider fonctionnement identique à Suna

## 🎯 RÉSULTAT ATTENDU

**Agent Orchestra Connect "Alex" qui :**
- ✅ S'appelle Alex et incarne Orchestra Connect
- ✅ Est sympa, souriant et professionnel
- ✅ A TOUS les outils nécessaires activés
- ✅ Utilise Firecrawl pour le scraping web
- ✅ Fonctionne exactement comme Suna
- ✅ Répond en français
- ✅ Peut extraire de vrais liens d'annonces

## 🚀 PROCHAINES ÉTAPES

1. **Implémenter les corrections** dans tools.ts
2. **Créer le prompt Alex optimisé**
3. **Créer l'agent Orchestra Connect parfait**
4. **Tester avec une vraie demande**
5. **Valider le fonctionnement complet**
