# 🤖 EXPLICATION : LES AGENTS DANS ORCHESTRA CONNECT

## 🎯 OUI, IL Y A PLUSIEURS AGENTS !

Orchestra Connect utilise un **système d'agents multiples** où vous pouvez créer et gérer différents agents spécialisés.

## 📋 TYPES D'AGENTS

### **1. AGENT PAR DÉFAUT (Suna-like)**
```python
# Quand aucun agent n'est spécifié
if agent_config is None:
    # Mode Suna complet - TOUS les outils activés
    thread_manager.add_tool(SandboxWebSearchTool, ...)
    thread_manager.add_tool(SandboxFilesTool, ...)
    # etc.
```

**Caractéristiques :**
- ✅ Tous les outils activés automatiquement
- ✅ Prompt Suna original
- ✅ Capacités complètes
- ✅ Pas de restrictions

### **2. AGENTS PERSONNALISÉS**
```python
# Quand un agent spécifique est choisi
if agent_config:
    # Seuls les outils enabled=true sont activés
    if enabled_tools.get('web_search_tool', {}).get('enabled', False):
        thread_manager.add_tool(SandboxWebSearchTool, ...)
```

**Caractéristiques :**
- 🎛️ Configuration personnalisée
- 🔧 Outils sélectionnés manuellement
- 📝 Prompt personnalisé
- 🎭 Avatar et couleur personnalisés
- 🏷️ Nom et description personnalisés

## 🏗️ ARCHITECTURE DES AGENTS

### **STRUCTURE D'UN AGENT**
```typescript
interface Agent {
  agent_id: string;           // ID unique
  name: string;              // "Alex - Orchestra Connect"
  description?: string;      // Description de l'agent
  system_prompt: string;     // Prompt personnalisé
  agentpress_tools: {        // Outils activés/désactivés
    'web_search_tool': { enabled: boolean },
    'sb_files_tool': { enabled: boolean },
    // ...
  };
  configured_mcps: Array;    // MCP configurés
  custom_mcps?: Array;       // MCP personnalisés
  avatar?: string;           // 🎭
  avatar_color?: string;     // #4F46E5
  is_default: boolean;       // Agent par défaut ou non
}
```

### **LOGIQUE DE SÉLECTION**
```python
# Dans run.py
async def run_agent(agent_config: Optional[dict] = None):
    if agent_config is None:
        # 🟢 MODE SUNA : Tous les outils
        logger.info("No agent specified - registering all tools")
    else:
        # 🟡 MODE AGENT PERSONNALISÉ : Outils sélectionnés
        logger.info(f"Using custom agent: {agent_config.get('name')}")
```

## 🎭 EXEMPLES D'AGENTS

### **AGENT 1 : Suna (par défaut)**
- **Nom** : Suna.so
- **Outils** : TOUS activés
- **Prompt** : Prompt Suna original
- **Usage** : Développement complet

### **AGENT 2 : Alex - Orchestra Connect**
- **Nom** : Alex - Orchestra Connect
- **Outils** : Sélectionnés (web_search, files, browser, vision)
- **Prompt** : Prompt Alex souriant en français
- **Usage** : Assistant personnel Orchestra Connect

### **AGENT 3 : Recherche Web Spécialisé**
- **Nom** : Web Researcher
- **Outils** : web_search_tool + sb_browser_tool uniquement
- **Prompt** : Spécialisé recherche et analyse web
- **Usage** : Recherche et veille

### **AGENT 4 : Développeur**
- **Nom** : Code Assistant
- **Outils** : sb_shell_tool + sb_files_tool + sb_deploy_tool
- **Prompt** : Spécialisé développement
- **Usage** : Développement et déploiement

## 🔧 COMMENT CRÉER/GÉRER LES AGENTS

### **INTERFACE WEB**
1. **Aller sur** `/dashboard/agents`
2. **Voir la liste** des agents existants
3. **Créer un nouvel agent** avec le bouton "+"
4. **Configurer** :
   - Nom et description
   - Prompt personnalisé
   - Outils activés/désactivés
   - MCP si nécessaire
   - Avatar et couleur

### **API**
```bash
# Créer un agent
POST /agents
{
  "name": "Alex - Orchestra Connect",
  "system_prompt": "...",
  "agentpress_tools": { ... }
}

# Lister les agents
GET /agents

# Utiliser un agent spécifique
POST /chat/start
{
  "agent_id": "agent-uuid-here"
}
```

## 🚨 LE PROBLÈME IDENTIFIÉ

### **AGENTS ORCHESTRA CONNECT CRÉÉS AVEC OUTILS DÉSACTIVÉS**
```typescript
// Dans tools.ts - Configuration par défaut
export const DEFAULT_AGENTPRESS_TOOLS = {
    'web_search_tool': { enabled: false },  // ❌ PROBLÈME !
    'sb_files_tool': { enabled: false },    // ❌ PROBLÈME !
    // ...
};
```

### **SOLUTION : CONFIGURATION ORCHESTRA CONNECT**
```typescript
// Nouvelle configuration optimisée
export const ORCHESTRA_CONNECT_TOOLS = {
    'web_search_tool': { enabled: true },   // ✅ ACTIVÉ
    'sb_files_tool': { enabled: true },     // ✅ ACTIVÉ
    'sb_browser_tool': { enabled: true },   // ✅ ACTIVÉ
    // ...
};
```

## 🎯 RECOMMANDATION

### **POUR ORCHESTRA CONNECT :**
1. **Créer un agent "Alex"** avec ORCHESTRA_CONNECT_TOOLS
2. **Utiliser le prompt Alex** souriant et en français
3. **Activer les outils essentiels** (web_search, files, browser, vision)
4. **Tester avec une vraie demande** de recherche web

### **RÉSULTAT ATTENDU :**
- 😊 Agent Alex souriant et professionnel
- 🇫🇷 Répond en français
- 🔍 Utilise Firecrawl pour scraper
- 📁 Gère les fichiers
- 🌐 Automatise les navigateurs
- 🎭 Incarne Orchestra Connect

## 💡 EN RÉSUMÉ

**OUI, il y a plusieurs agents !** Vous pouvez :
- Utiliser l'agent par défaut (mode Suna complet)
- Créer des agents personnalisés spécialisés
- Configurer les outils pour chaque agent
- Avoir différentes personnalités et prompts

**Le problème était que les agents Orchestra Connect étaient créés avec tous les outils désactivés par défaut, contrairement à l'agent Suna qui a tout activé.**
