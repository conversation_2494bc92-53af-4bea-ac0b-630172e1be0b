'use client';

import { useSubscription } from '@/hooks/react-query/subscriptions/use-subscriptions';
import { useState, useEffect, useMemo } from 'react';
import { isLocalMode } from '@/lib/config';
import { useAvailableModels } from '@/hooks/react-query/subscriptions/use-model';

export const STORAGE_KEY_MODEL = 'suna-preferred-model';
export const STORAGE_KEY_CUSTOM_MODELS = 'customModels';
export const DEFAULT_FREE_MODEL_ID = 'super-agent';
export const DEFAULT_PREMIUM_MODEL_ID = 'super-agent';

export type SubscriptionStatus = 'no_subscription' | 'active';

export interface ModelOption {
  id: string;
  label: string;
  requiresSubscription: boolean;
  description?: string;
  top?: boolean;
  isCustom?: boolean;
  priority?: number;
  color?: string;
  capabilities?: string[];
}

export interface CustomModel {
  id: string;
  label: string;
}

// SINGLE SOURCE OF TRUTH for all model data - Simplified business-friendly agents
export const MODELS = {
  // Super Agent (Gemini Flash) - disponible pour tous
  'super-agent': {
    tier: 'free',
    priority: 100,
    recommended: true,
    lowQuality: false,
    description: 'L\'agent intelligent pour tous vos besoins - Rapide, efficace et polyvalent',
    backendModel: 'gemini-flash-2.5',
    color: 'green',
    capabilities: ['Polyvalent', 'Rapide', 'Économique']
  },

  // Agent Stratégique (Gemini Pro) - pour analyses complexes
  'agent-strategique': {
    tier: 'premium',
    priority: 90,
    recommended: true,
    lowQuality: false,
    description: 'Spécialisé dans l\'analyse approfondie et la planification stratégique',
    backendModel: 'google/gemini-2.5-pro-preview',
    color: 'blue',
    capabilities: ['Analyse complexe', 'Stratégie', 'Réflexion']
  },

  // Agent Design (GPT Image) - pour créativité et images
  'agent-design': {
    tier: 'premium',
    priority: 85,
    recommended: true,
    lowQuality: false,
    description: 'Expert en créativité, génération d\'images et communication',
    backendModel: 'gpt-image-1',
    color: 'orange',
    capabilities: ['Créativité', 'Images', 'Communication']
  },

  // Modèles techniques cachés (pour compatibilité backend)
  'gemini-flash-2.5': {
    tier: 'free',
    priority: 0,
    recommended: false,
    lowQuality: false,
    description: 'Gemini Flash - Google\'s faster, more efficient model',
    hidden: true
  },
  'google/gemini-2.5-pro-preview': {
    tier: 'premium',
    priority: 0,
    recommended: false,
    lowQuality: false,
    description: 'Gemini Pro 2.5 - Google\'s latest powerful model',
    hidden: true
  },
  'gpt-4.1': {
    tier: 'premium',
    priority: 0,
    recommended: false,
    lowQuality: false,
    description: 'GPT-4.1 - OpenAI\'s most advanced model',
    hidden: true
  },
};

// Model tier definitions
export const MODEL_TIERS = {
  premium: {
    requiresSubscription: true,
    baseDescription: 'Advanced model with superior capabilities'
  },
  free: {
    requiresSubscription: false,
    baseDescription: 'Available to all users'
  },
  custom: {
    requiresSubscription: false,
    baseDescription: 'User-defined model'
  }
};

// Helper to check if a user can access a model based on subscription status
export const canAccessModel = (
  subscriptionStatus: SubscriptionStatus,
  requiresSubscription: boolean,
): boolean => {
  if (isLocalMode()) {
    return true;
  }
  return subscriptionStatus === 'active' || !requiresSubscription;
};

// Helper to format a model name for display
export const formatModelName = (name: string): string => {
  return name
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Add openrouter/ prefix to custom models
export const getPrefixedModelId = (modelId: string, isCustom: boolean): string => {
  if (isCustom && !modelId.startsWith('openrouter/')) {
    return `openrouter/${modelId}`;
  }
  return modelId;
};

// Helper to get custom models from localStorage
export const getCustomModels = (): CustomModel[] => {
  if (!isLocalMode() || typeof window === 'undefined') return [];
  
  try {
    const storedModels = localStorage.getItem(STORAGE_KEY_CUSTOM_MODELS);
    if (!storedModels) return [];
    
    const parsedModels = JSON.parse(storedModels);
    if (!Array.isArray(parsedModels)) return [];
    
    return parsedModels
      .filter((model: any) => 
        model && typeof model === 'object' && 
        typeof model.id === 'string' && 
        typeof model.label === 'string');
  } catch (e) {
    console.error('Error parsing custom models:', e);
    return [];
  }
};

// Helper to save model preference to localStorage safely
const saveModelPreference = (modelId: string): void => {
  try {
    localStorage.setItem(STORAGE_KEY_MODEL, modelId);
  } catch (error) {
    console.warn('Failed to save model preference to localStorage:', error);
  }
};

export const useModelSelection = () => {
  const [selectedModel, setSelectedModel] = useState(DEFAULT_FREE_MODEL_ID);
  const [customModels, setCustomModels] = useState<CustomModel[]>([]);
  
  const { data: subscriptionData } = useSubscription();
  const { data: modelsData, isLoading: isLoadingModels } = useAvailableModels({
    refetchOnMount: false,
  });
  
  const subscriptionStatus: SubscriptionStatus = subscriptionData?.status === 'active' 
    ? 'active' 
    : 'no_subscription';

  // Function to refresh custom models from localStorage
  const refreshCustomModels = () => {
    if (isLocalMode() && typeof window !== 'undefined') {
      const freshCustomModels = getCustomModels();
      setCustomModels(freshCustomModels);
    }
  };

  // Load custom models from localStorage
  useEffect(() => {
    refreshCustomModels();
  }, []);

  // Generate model options list with simplified business-friendly agents
  const MODEL_OPTIONS = useMemo(() => {
    // Use our simplified business agents instead of complex technical models
    let models = [
      {
        id: 'super-agent',
        label: 'Super Agent',
        requiresSubscription: false,
        description: MODELS['super-agent'].description,
        priority: MODELS['super-agent'].priority,
        lowQuality: MODELS['super-agent'].lowQuality,
        recommended: MODELS['super-agent'].recommended,
        color: MODELS['super-agent'].color,
        capabilities: MODELS['super-agent'].capabilities
      },
      {
        id: 'agent-strategique',
        label: 'Agent Stratégique',
        requiresSubscription: true,
        description: MODELS['agent-strategique'].description,
        priority: MODELS['agent-strategique'].priority,
        lowQuality: MODELS['agent-strategique'].lowQuality,
        recommended: MODELS['agent-strategique'].recommended,
        color: MODELS['agent-strategique'].color,
        capabilities: MODELS['agent-strategique'].capabilities
      },
      {
        id: 'agent-design',
        label: 'Agent Design',
        requiresSubscription: true,
        description: MODELS['agent-design'].description,
        priority: MODELS['agent-design'].priority,
        lowQuality: MODELS['agent-design'].lowQuality,
        recommended: MODELS['agent-design'].recommended,
        color: MODELS['agent-design'].color,
        capabilities: MODELS['agent-design'].capabilities
      }
    ];
    
    // Add custom models if in local mode
    if (isLocalMode() && customModels.length > 0) {
      const customModelOptions = customModels.map(model => ({
        id: model.id,
        label: model.label || formatModelName(model.id),
        requiresSubscription: false,
        description: MODEL_TIERS.custom.baseDescription,
        top: false,
        isCustom: true,
        priority: 30, // Low priority by default
        lowQuality: false,
        recommended: false,
        color: 'gray', // Default color for custom models
        capabilities: [] // No capabilities for custom models
      }));
      
      models = [...models, ...customModelOptions];
    }
    
    // Sort models consistently in one place:
    // 1. First by free/premium (free first)
    // 2. Then by priority (higher first)
    // 3. Finally by name (alphabetical)
    return models.sort((a, b) => {
      // First by free/premium status
      if (a.requiresSubscription !== b.requiresSubscription) {
        return a.requiresSubscription ? 1 : -1;
      }
      
      // Then by priority (higher first)
      if (a.priority !== b.priority) {
        return b.priority - a.priority;
      }
      
      // Finally by name
      return a.label.localeCompare(b.label);
    });
  }, [modelsData, isLoadingModels, customModels]);

  // Get filtered list of models the user can access (no additional sorting)
  const availableModels = useMemo(() => {
    return isLocalMode() 
      ? MODEL_OPTIONS 
      : MODEL_OPTIONS.filter(model => 
          canAccessModel(subscriptionStatus, model.requiresSubscription)
        );
  }, [MODEL_OPTIONS, subscriptionStatus]);

  // Initialize selected model from localStorage or defaults
  useEffect(() => {
    if (typeof window === 'undefined') return;

    try {
      const savedModel = localStorage.getItem(STORAGE_KEY_MODEL);

      // Migration: Convert old model names to new business-friendly agent names
      let migratedModel = savedModel;
      if (savedModel) {
        const migrationMap: Record<string, string> = {
          'agent': 'super-agent', // Migrate old agent to super-agent
          'gemini-flash-2.5': 'super-agent',
          'gemini-2.5-flash:thinking': 'super-agent',
          'openrouter/google/gemini-2.5-flash-preview-05-20': 'super-agent',
          'openrouter/google/gemini-2.5-flash-preview-05-20:thinking': 'super-agent',
          'gemini-2.5-pro-preview': 'agent-strategique',
          'openrouter/google/gemini-2.5-pro-preview': 'agent-strategique',
          'gpt-4.1': 'agent-design',
          'openai/gpt-4.1': 'agent-design',
        };

        if (migrationMap[savedModel]) {
          migratedModel = migrationMap[savedModel];
          // Save the migrated model name
          localStorage.setItem(STORAGE_KEY_MODEL, migratedModel);
        }
      }
      
      // Local mode - allow any model
      if (isLocalMode()) {
        if (migratedModel && MODEL_OPTIONS.find(option => option.id === migratedModel)) {
          setSelectedModel(migratedModel);
        } else {
          setSelectedModel(DEFAULT_PREMIUM_MODEL_ID);
          saveModelPreference(DEFAULT_PREMIUM_MODEL_ID);
        }
        return;
      }
      
      // Premium subscription - ALWAYS use premium model
      if (subscriptionStatus === 'active') {
        // If they had a premium model saved and it's still valid, use it
        const hasSavedPremiumModel = migratedModel &&
          MODEL_OPTIONS.find(option =>
            option.id === migratedModel &&
            option.requiresSubscription &&
            canAccessModel(subscriptionStatus, true)
          );

        // Otherwise use the default premium model
        if (hasSavedPremiumModel) {
          setSelectedModel(migratedModel!);
        } else {
          setSelectedModel(DEFAULT_PREMIUM_MODEL_ID);
          saveModelPreference(DEFAULT_PREMIUM_MODEL_ID);
        }
        return;
      }
      
      // No subscription - use saved model if accessible (free tier), or default free
      if (migratedModel) {
        const modelOption = MODEL_OPTIONS.find(option => option.id === migratedModel);
        if (modelOption && canAccessModel(subscriptionStatus, modelOption.requiresSubscription)) {
          setSelectedModel(migratedModel);
        } else {
          setSelectedModel(DEFAULT_FREE_MODEL_ID);
          saveModelPreference(DEFAULT_FREE_MODEL_ID);
        }
      } else {
        setSelectedModel(DEFAULT_FREE_MODEL_ID);
        saveModelPreference(DEFAULT_FREE_MODEL_ID);
      }
    } catch (error) {
      console.warn('Failed to load preferences from localStorage:', error);
      setSelectedModel(DEFAULT_FREE_MODEL_ID);
    }
  }, [subscriptionStatus, MODEL_OPTIONS]);

  // Handle model selection change
  const handleModelChange = (modelId: string) => {
    console.log('handleModelChange', modelId);
    
    // Refresh custom models from localStorage to ensure we have the latest
    if (isLocalMode()) {
      refreshCustomModels();
    }
    
    // First check if it's a custom model in local mode
    const isCustomModel = isLocalMode() && customModels.some(model => model.id === modelId);
    
    // Then check if it's in standard MODEL_OPTIONS
    const modelOption = MODEL_OPTIONS.find(option => option.id === modelId);
    
    // Check if model exists in either custom models or standard options
    if (!modelOption && !isCustomModel) {
      console.warn('Model not found in options:', modelId, MODEL_OPTIONS, isCustomModel, customModels);
      
      // Reset to default model when the selected model is not found
      const defaultModel = isLocalMode() ? DEFAULT_PREMIUM_MODEL_ID : DEFAULT_FREE_MODEL_ID;
      setSelectedModel(defaultModel);
      saveModelPreference(defaultModel);
      return;
    }

    // Check access permissions (except for custom models in local mode)
    if (!isCustomModel && !isLocalMode() && 
        !canAccessModel(subscriptionStatus, modelOption?.requiresSubscription ?? false)) {
      console.warn('Model not accessible:', modelId);
      return;
    }
    console.log('setting selected model', modelId);
    setSelectedModel(modelId);
    saveModelPreference(modelId);
  };

  // Get the actual model ID to send to the backend
  const getActualModelId = (modelId: string): string => {
    // Map business-friendly agent names to actual backend model IDs
    const modelData = MODELS[modelId];
    if (modelData && modelData.backendModel) {
      return modelData.backendModel;
    }
    // Fallback to the original ID if no mapping found
    return modelId;
  };

  return {
    selectedModel,
    setSelectedModel: (modelId: string) => {
      handleModelChange(modelId);
    },
    subscriptionStatus,
    availableModels,
    allModels: MODEL_OPTIONS,  // Already pre-sorted
    customModels,
    getActualModelId,
    refreshCustomModels,
    canAccessModel: (modelId: string) => {
      if (isLocalMode()) return true;
      const model = MODEL_OPTIONS.find(m => m.id === modelId);
      return model ? canAccessModel(subscriptionStatus, model.requiresSubscription) : false;
    },
    isSubscriptionRequired: (modelId: string) => {
      return MODEL_OPTIONS.find(m => m.id === modelId)?.requiresSubscription || false;
    }
  };
};

// Export the hook but not any sorting logic - sorting is handled internally