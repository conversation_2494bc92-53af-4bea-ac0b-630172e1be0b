'use client';

import React from 'react';
import { Brain, Sparkles, BarChart3 } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface AgentToggleSelectorProps {
  selectedAgentId?: string;
  onAgentSelect: (agentId: string) => void;
  className?: string;
}

// Agents optimisés pour PME, cadres, étudiants
const AGENTS = {
  'super-agent': {
    name: 'Super Agent',
    shortName: 'Super',
    description: 'Agent polyvalent pour tous vos besoins quotidiens',
    capabilities: ['Rédaction', 'Analyse', 'Recherche', 'Conseil'],
    icon: Brain,
    color: 'green',
    isDefault: true,
    targetUsers: 'Idéal pour PME, cadres et étudiants'
  },
  'creative-agent': {
    name: 'Agent Créati<PERSON>',
    shortName: 'Créatif',
    description: 'Spécialisé dans la création de contenu et le marketing',
    capabilities: ['Contenu', 'Marketing', 'Design', 'Communication'],
    icon: Sparkles,
    color: 'orange',
    isDefault: false,
    targetUsers: 'Parfait pour les équipes marketing et communication'
  },
  'analyst-agent': {
    name: 'Agent Analyste',
    shortName: 'Analyste',
    description: 'Expert en analyse de données et insights business',
    capabilities: ['Données', 'Tableaux', 'Rapports', 'KPI'],
    icon: BarChart3,
    color: 'blue',
    isDefault: false,
    targetUsers: 'Essentiel pour les décideurs et analystes'
  }
};

export function AgentToggleSelector({
  selectedAgentId = 'super-agent',
  onAgentSelect,
  className
}: AgentToggleSelectorProps) {
  return (
    <div className={cn('flex items-center gap-1', className)}>
      {Object.entries(AGENTS).map(([agentId, agent]) => {
        const isSelected = selectedAgentId === agentId;
        const IconComponent = agent.icon;
        
        return (
          <TooltipProvider key={agentId}>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  onClick={() => onAgentSelect(agentId)}
                  className={cn(
                    'relative flex items-center gap-1.5 px-2.5 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ease-out',
                    'hover:scale-105 active:scale-95',
                    'focus:outline-none focus:ring-2 focus:ring-offset-1',
                    // Style iPhone toggle - sélectionné
                    isSelected && agent.color === 'green' && 'bg-green-500 text-white shadow-md shadow-green-200 dark:shadow-green-900/50',
                    isSelected && agent.color === 'orange' && 'bg-orange-500 text-white shadow-md shadow-orange-200 dark:shadow-orange-900/50',
                    isSelected && agent.color === 'blue' && 'bg-blue-500 text-white shadow-md shadow-blue-200 dark:shadow-blue-900/50',
                    // Style iPhone toggle - non sélectionné
                    !isSelected && 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700',
                    // Focus states
                    isSelected && agent.color === 'green' && 'focus:ring-green-300',
                    isSelected && agent.color === 'orange' && 'focus:ring-orange-300',
                    isSelected && agent.color === 'blue' && 'focus:ring-blue-300',
                    !isSelected && 'focus:ring-gray-300'
                  )}
                >
                  <IconComponent className="h-3 w-3" />
                  <span>{agent.shortName}</span>
                  
                  {agent.isDefault && isSelected && (
                    <div className="absolute -top-1 -right-1 w-2 h-2 bg-white rounded-full border border-green-500" />
                  )}
                </button>
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <IconComponent className="h-4 w-4" />
                    <span className="font-semibold">{agent.name}</span>
                    {agent.isDefault && (
                      <Badge variant="secondary" className="text-xs px-1.5 py-0">
                        Recommandé
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">{agent.description}</p>
                  <div className="space-y-1">
                    <p className="text-xs font-medium">Capacités :</p>
                    <div className="flex flex-wrap gap-1">
                      {agent.capabilities.map((capability) => (
                        <Badge key={capability} variant="outline" className="text-xs px-1.5 py-0">
                          {capability}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground italic">{agent.targetUsers}</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      })}
    </div>
  );
}
