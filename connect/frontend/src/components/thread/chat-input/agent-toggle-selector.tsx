'use client';

import React from 'react';
import { <PERSON>, <PERSON>rk<PERSON>, BarChart3 } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface AgentToggleSelectorProps {
  selectedAgentId?: string;
  onAgentSelect: (agentId: string) => void;
  onModelChange?: (modelId: string) => void; // For model selection integration
  className?: string;
}

// Agents optimisés pour PME, cadres, étudiants avec design aurora pastel
const AGENTS = {
  'super-agent': {
    name: 'Super Agent',
    shortName: 'Super Agent',
    description: 'Agent polyvalent pour tous vos besoins quotidiens',
    capabilities: ['Rédaction', 'Analyse', 'Recherche', 'Conseil'],
    icon: Brain,
    color: 'aurora-green',
    isDefault: true,
    targetUsers: 'Id<PERSON><PERSON> pour PME, cadres et étudiants',
    modelId: 'super-agent' // Maps to gemini-flash-2.5 in backend
  },
  'agent-design': {
    name: 'Agent Créatif',
    shortName: 'Créatif',
    description: 'Spécialisé dans la création de contenu et le marketing',
    capabilities: ['Contenu', 'Marketing', 'Design', 'Communication'],
    icon: Sparkles,
    color: 'aurora-rainbow',
    isDefault: false,
    targetUsers: 'Parfait pour les équipes marketing et communication',
    modelId: 'agent-design' // Maps to gpt-image-1 in backend
  },
  'agent-strategique': {
    name: 'Agent Stratégie',
    shortName: 'Stratégie',
    description: 'Expert en analyse de données et insights business',
    capabilities: ['Données', 'Tableaux', 'Rapports', 'KPI'],
    icon: BarChart3,
    color: 'aurora-blue',
    isDefault: false,
    targetUsers: 'Essentiel pour les décideurs et analystes',
    modelId: 'agent-strategique' // Maps to google/gemini-2.5-pro-preview in backend
  }
};

export function AgentToggleSelector({
  selectedAgentId = 'super-agent',
  onAgentSelect,
  onModelChange,
  className
}: AgentToggleSelectorProps) {

  const handleAgentSelect = (agentId: string) => {
    onAgentSelect(agentId);
    // Also trigger model change if provided
    const agent = AGENTS[agentId as keyof typeof AGENTS];
    if (onModelChange && agent?.modelId) {
      onModelChange(agent.modelId);
    }
  };

  return (
    <div className={cn('flex items-center', className)}>
      {/* Container with subtle background and border */}
      <div className="flex items-center gap-1 p-1 rounded-full bg-gray-50/80 dark:bg-gray-900/50 border border-gray-200/50 dark:border-gray-700/50 backdrop-blur-sm">
        {Object.entries(AGENTS).map(([agentId, agent]) => {
          const isSelected = selectedAgentId === agentId;
          const IconComponent = agent.icon;

          return (
            <TooltipProvider key={agentId}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={() => handleAgentSelect(agentId)}
                    className={cn(
                      'relative flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-300 ease-out',
                      'hover:scale-105 active:scale-95',
                      'focus:outline-none focus:ring-2 focus:ring-offset-1',
                      // Aurora Green - Super Agent
                      isSelected && agent.color === 'aurora-green' && 'bg-gradient-to-r from-emerald-300/80 via-green-300/80 to-teal-300/80 text-emerald-800 shadow-lg shadow-emerald-200/50 dark:from-emerald-400/60 dark:via-green-400/60 dark:to-teal-400/60 dark:text-emerald-100 dark:shadow-emerald-900/30',
                      // Aurora Rainbow - Creative Agent
                      isSelected && agent.color === 'aurora-rainbow' && 'bg-gradient-to-r from-pink-300/80 via-purple-300/80 via-blue-300/80 to-cyan-300/80 text-purple-800 shadow-lg shadow-purple-200/50 dark:from-pink-400/60 dark:via-purple-400/60 dark:via-blue-400/60 dark:to-cyan-400/60 dark:text-purple-100 dark:shadow-purple-900/30',
                      // Aurora Blue - Strategy Agent
                      isSelected && agent.color === 'aurora-blue' && 'bg-gradient-to-r from-blue-300/80 via-indigo-300/80 to-purple-300/80 text-blue-800 shadow-lg shadow-blue-200/50 dark:from-blue-400/60 dark:via-indigo-400/60 dark:to-purple-400/60 dark:text-blue-100 dark:shadow-blue-900/30',
                      // Non-selected state
                      !isSelected && 'bg-white/60 text-gray-600 hover:bg-white/80 dark:bg-gray-800/60 dark:text-gray-400 dark:hover:bg-gray-800/80 border border-gray-200/30 dark:border-gray-700/30',
                      // Focus states with aurora colors
                      isSelected && agent.color === 'aurora-green' && 'focus:ring-emerald-300/50',
                      isSelected && agent.color === 'aurora-rainbow' && 'focus:ring-purple-300/50',
                      isSelected && agent.color === 'aurora-blue' && 'focus:ring-blue-300/50',
                      !isSelected && 'focus:ring-gray-300/50'
                    )}
                  >
                    <IconComponent className="h-3 w-3" />
                    <span className="whitespace-nowrap">{agent.shortName}</span>

                    {agent.isDefault && isSelected && (
                      <div className="absolute -top-0.5 -right-0.5 w-2 h-2 bg-white rounded-full border border-emerald-400 shadow-sm" />
                    )}
                  </button>
                </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <IconComponent className="h-4 w-4" />
                    <span className="font-semibold">{agent.name}</span>
                    {agent.isDefault && (
                      <Badge variant="secondary" className="text-xs px-1.5 py-0">
                        Recommandé
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">{agent.description}</p>
                  <div className="space-y-1">
                    <p className="text-xs font-medium">Capacités :</p>
                    <div className="flex flex-wrap gap-1">
                      {agent.capabilities.map((capability) => (
                        <Badge key={capability} variant="outline" className="text-xs px-1.5 py-0">
                          {capability}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground italic">{agent.targetUsers}</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      })}
      </div>
    </div>
  );
}
