'use client';

import { Info, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';

interface BillingErrorAlertProps {
  message?: string;
  currentUsage?: number;
  limit?: number;
  accountId?: string | null;
  onDismiss: () => void;
  isOpen: boolean;
}

export function BillingErrorAlert({
  message,
  currentUsage,
  limit,
  accountId,
  onDismiss,
  isOpen,
}: BillingErrorAlertProps) {
  const router = useRouter();

  if (!isOpen) return null;

  return (
    <div className="fixed bottom-4 right-4 z-[9999]">
      <div className="bg-blue-50/95 dark:bg-blue-950/95 backdrop-blur-sm border border-blue-200/50 dark:border-blue-800/50 rounded-xl p-5 shadow-xl max-w-md">
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0 bg-blue-100/80 dark:bg-blue-900/80 p-2 rounded-full">
            <Info className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="flex-1">
            <div className="flex justify-between items-start mb-2">
              <h3 className="text-sm font-semibold text-blue-800 dark:text-blue-200">
                Limite d'utilisation atteinte
              </h3>
              <Button
                variant="ghost"
                size="icon"
                onClick={onDismiss}
                className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-blue-700/80 dark:text-blue-300/80 mb-3">
              {message || "Vous avez atteint la limite de votre forfait actuel. Passez à un forfait supérieur pour continuer."}
            </p>

            {/* Usage Information */}
            {(currentUsage !== undefined || limit !== undefined) && (
              <div className="bg-blue-50/50 dark:bg-blue-900/30 rounded-lg p-3 mb-3">
                <div className="flex justify-between items-center text-xs">
                  <span className="text-blue-700 dark:text-blue-300">Utilisation ce mois :</span>
                  <span className="font-medium text-blue-800 dark:text-blue-200">
                    {currentUsage?.toFixed(0) || '0'} / {limit || '30'} crédits
                  </span>
                </div>
                <div className="mt-1 bg-blue-200/50 dark:bg-blue-800/50 rounded-full h-1.5">
                  <div
                    className="bg-blue-600 dark:bg-blue-400 h-1.5 rounded-full transition-all duration-300"
                    style={{
                      width: `${Math.min(100, ((currentUsage || 0) / (limit || 30)) * 100)}%`
                    }}
                  />
                </div>
              </div>
            )}

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onDismiss}
                className="text-xs border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/50"
              >
                Fermer
              </Button>
              <Button
                size="sm"
                onClick={() =>
                  router.push(`/settings/billing?accountId=${accountId}`)
                }
                className="text-xs bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700 text-white shadow-md"
              >
                Améliorer le forfait
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
