import React, { useEffect, useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Zap } from 'lucide-react';
import { useModal } from '@/hooks/use-modal-store';
import { PricingSectionCompact } from './pricing-section-compact';
import { getSubscription, SubscriptionStatus } from '@/lib/api';
import { useAuth } from '@/components/AuthProvider';

const returnUrl = process.env.NEXT_PUBLIC_URL || 'http://localhost:3000';

export const PaymentRequiredDialog = () => {
  const { isOpen, type, onClose } = useModal();
  const { session } = useAuth();
  const isModalOpen = isOpen && type === 'paymentRequiredDialog';
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionStatus | null>(null);

  useEffect(() => {
    async function fetchSubscription() {
      if (!isModalOpen || !session) return;

      try {
        const data = await getSubscription();
        setSubscriptionData(data);
      } catch (error) {
        console.error('Failed to get subscription:', error);
      }
    }

    fetchSubscription();
  }, [isModalOpen, session]);

  return (
    <Dialog open={isModalOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95vw] max-w-[600px] max-h-[85vh] overflow-hidden flex flex-col p-0">
        <DialogHeader className="px-6 pt-6 flex-shrink-0">
          <DialogTitle className="text-xl font-semibold">
            Limite d'utilisation atteinte
          </DialogTitle>
          <DialogDescription className="text-sm text-muted-foreground">
            Vous avez épuisé votre forfait pour cette période. Passez à un forfait premium pour continuer à utiliser Alex.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 pb-4 overflow-y-auto scrollbar-thin scrollbar-thumb-zinc-300 dark:scrollbar-thumb-zinc-700 scrollbar-track-transparent px-6 min-h-0">
          <div className="space-y-6 pb-4">
            <div className="flex items-start p-4 bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  <Zap className="w-5 h-5 text-amber-600 dark:text-amber-400" />
                </div>
                <div className="text-sm min-w-0 flex-1">
                  <p className="font-medium text-amber-800 dark:text-amber-200">
                    Forfait épuisé
                  </p>
                  <p className="text-amber-700 dark:text-amber-300 break-words mb-3">
                    Votre forfait actuel a été entièrement utilisé pour cette période de facturation.
                  </p>

                  {/* Usage Information */}
                  {subscriptionData && (
                    <div className="bg-amber-100/50 dark:bg-amber-900/30 rounded-lg p-3">
                      <div className="flex justify-between items-center text-xs mb-2">
                        <span className="text-amber-700 dark:text-amber-300">Utilisation ce mois :</span>
                        <span className="font-medium text-amber-800 dark:text-amber-200">
                          {subscriptionData.current_usage?.toFixed(0) || '0'} / {subscriptionData.minutes_limit || '30'} crédits
                        </span>
                      </div>
                      <div className="bg-amber-200/50 dark:bg-amber-800/50 rounded-full h-1.5">
                        <div
                          className="bg-amber-600 dark:bg-amber-400 h-1.5 rounded-full transition-all duration-300"
                          style={{
                            width: `${Math.min(100, ((subscriptionData.current_usage || 0) / (subscriptionData.minutes_limit || 30)) * 100)}%`
                          }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="w-full">
              <PricingSectionCompact
                returnUrl={`${returnUrl}/dashboard`}
                hideFree={true}
              />
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
