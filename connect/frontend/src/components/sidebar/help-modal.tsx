import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogTitle,
  DialogHeader,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { HelpCircle, Sparkles, Zap, Brain, Lightbulb, Target, BarChart3 } from 'lucide-react';

export function HelpModal() {
  const [open, setOpen] = useState(false);

  const modelGuide = [
    {
      name: 'Gemini 2.5 Flash',
      icon: <Zap className="h-6 w-6" />,
      gradient: 'from-green-400 to-blue-500',
      bgGlass: 'bg-gradient-to-br from-green-50/80 to-blue-50/80 dark:from-green-950/40 dark:to-blue-950/40 backdrop-blur-sm',
      borderGlass: 'border-green-200/50 dark:border-green-800/50',
      bestFor: [
        { icon: <Target className="h-3 w-3" />, text: 'Emails clients et prospects' },
        { icon: <BarChart3 className="h-3 w-3" />, text: 'Analyses de rapports' },
        { icon: <Lightbulb className="h-3 w-3" />, text: 'Contenu marketing' },
        { icon: <Sparkles className="h-3 w-3" />, text: 'Traductions professionnelles' }
      ],
      description: 'Idéal pour 80% de vos besoins quotidiens. Rapide, fiable et parfaitement adapté aux tâches business courantes.',
      recommended: true,
      usage: '80% de vos demandes'
    },
    {
      name: 'ChatGPT (GPT-4o)',
      icon: <Brain className="h-6 w-6" />,
      gradient: 'from-blue-400 to-purple-500',
      bgGlass: 'bg-gradient-to-br from-blue-50/80 to-purple-50/80 dark:from-blue-950/40 dark:to-purple-950/40 backdrop-blur-sm',
      borderGlass: 'border-blue-200/50 dark:border-blue-800/50',
      bestFor: [
        { icon: <Brain className="h-3 w-3" />, text: 'Développement d\'applications' },
        { icon: <BarChart3 className="h-3 w-3" />, text: 'Analyses financières poussées' },
        { icon: <Target className="h-3 w-3" />, text: 'Stratégies complexes' }
      ],
      description: 'Pour les tâches nécessitant une réflexion logique avancée et des analyses complexes.',
      recommended: false,
      usage: '15% de vos demandes'
    },
    {
      name: 'Claude 4 Sonnet',
      icon: <Sparkles className="h-6 w-6" />,
      gradient: 'from-purple-400 to-pink-500',
      bgGlass: 'bg-gradient-to-br from-purple-50/80 to-pink-50/80 dark:from-purple-950/40 dark:to-pink-950/40 backdrop-blur-sm',
      borderGlass: 'border-purple-200/50 dark:border-purple-800/50',
      bestFor: [
        { icon: <Sparkles className="h-3 w-3" />, text: 'Stratégie d\'entreprise' },
        { icon: <BarChart3 className="h-3 w-3" />, text: 'Audits complets' },
        { icon: <Lightbulb className="h-3 w-3" />, text: 'Recherche approfondie' }
      ],
      description: 'L\'expert pour les missions critiques nécessitant une expertise métier pointue.',
      recommended: false,
      usage: '5% de vos demandes'
    }
  ];



  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full text-xs bg-blue-50/50 hover:bg-blue-100/50 dark:bg-blue-950/30 dark:hover:bg-blue-900/40 border-blue-200/50 dark:border-blue-800/50 text-blue-700 dark:text-blue-300 backdrop-blur-sm transition-all duration-200"
        >
          <HelpCircle className="mr-1.5 h-3.5 w-3.5" />
          Guide & Astuces
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5 text-blue-600" />
            Guide d'utilisation d'Orchestra Connect
          </DialogTitle>
        </DialogHeader>
        
        <Tabs defaultValue="models" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="models">Choisir son modèle</TabsTrigger>
            <TabsTrigger value="prompts">Améliorer ses prompts</TabsTrigger>
          </TabsList>
          
          <TabsContent value="models" className="space-y-6 max-h-[60vh] overflow-y-auto">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">Choisissez le bon modèle pour votre besoin</h3>
              <p className="text-sm text-muted-foreground">
                Optimisez vos résultats en sélectionnant le modèle adapté à votre tâche
              </p>
            </div>

            <div className="grid gap-4">
              {modelGuide.map((model, index) => (
                <Card key={index} className={`relative overflow-hidden transition-all duration-300 hover:scale-[1.02] ${model.bgGlass} border ${model.borderGlass} ${model.recommended ? 'ring-2 ring-green-400/30 shadow-lg' : 'shadow-md'}`}>
                  {model.recommended && (
                    <div className="absolute -top-1 -right-1">
                      <Badge className="bg-gradient-to-r from-green-500 to-blue-500 text-white shadow-lg">
                        ⭐ Recommandé
                      </Badge>
                    </div>
                  )}

                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg bg-gradient-to-r ${model.gradient} text-white shadow-md`}>
                          {model.icon}
                        </div>
                        <div>
                          <CardTitle className="text-lg">{model.name}</CardTitle>
                          <Badge variant="outline" className="mt-1 text-xs">
                            {model.usage}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <CardDescription className="text-sm leading-relaxed mt-2">
                      {model.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent>
                    <div>
                      <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                        <Target className="h-4 w-4 text-green-600" />
                        Exemples d'usage business :
                      </h4>
                      <div className="grid gap-2">
                        {model.bestFor.map((use, i) => (
                          <div key={i} className="flex items-center gap-2 text-xs bg-white/50 dark:bg-black/20 rounded-md p-2 backdrop-blur-sm">
                            <div className="text-green-600">
                              {use.icon}
                            </div>
                            <span>{use.text}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="text-center p-4 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20 rounded-lg border border-green-200/50 dark:border-green-800/50">
              <p className="text-sm text-muted-foreground">
                💡 <strong>Conseil :</strong> Commencez toujours par Gemini Flash pour vos tâches courantes.
                Vous pouvez toujours changer de modèle si besoin !
              </p>
            </div>
          </TabsContent>
          
          <TabsContent value="prompts" className="space-y-4 max-h-[60vh] overflow-y-auto">
            <div className="grid gap-6">
              {/* Best Practices pour Orchestra Connect */}
              <div className="space-y-4">
                <div className="text-center space-y-2">
                  <h3 className="font-semibold text-xl">🎯 Comment maximiser Orchestra Connect</h3>
                  <p className="text-sm text-muted-foreground">
                    Découvrez comment tirer le meilleur parti de votre agent IA français
                  </p>
                </div>

                {/* Informations essentielles */}
                <Card className="bg-gradient-to-br from-blue-50/50 to-green-50/50 dark:from-blue-950/20 dark:to-green-950/20 border-blue-200/50 dark:border-blue-800/50">
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Target className="h-4 w-4 text-blue-600" />
                      Informations clés à donner à Orchestra Connect
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid gap-3">
                      <div className="flex items-start gap-2">
                        <span className="text-green-600 font-bold">✓</span>
                        <div>
                          <p className="font-medium text-sm">Votre secteur d'activité</p>
                          <p className="text-xs text-muted-foreground">Ex: "Je suis consultant en marketing digital"</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <span className="text-green-600 font-bold">✓</span>
                        <div>
                          <p className="font-medium text-sm">Le contexte de votre demande</p>
                          <p className="text-xs text-muted-foreground">Ex: "Pour un client dans la restauration"</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <span className="text-green-600 font-bold">✓</span>
                        <div>
                          <p className="font-medium text-sm">L'objectif précis</p>
                          <p className="text-xs text-muted-foreground">Ex: "Augmenter les réservations de 30%"</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-2">
                        <span className="text-green-600 font-bold">✓</span>
                        <div>
                          <p className="font-medium text-sm">Le format souhaité</p>
                          <p className="text-xs text-muted-foreground">Ex: "Présentation PowerPoint de 10 slides"</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Exemples de prompts efficaces */}
                <Card className="bg-gradient-to-br from-green-50/50 to-blue-50/50 dark:from-green-950/20 dark:to-blue-950/20 border-green-200/50 dark:border-green-800/50">
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Lightbulb className="h-4 w-4 text-green-600" />
                      Exemples de prompts optimisés
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="space-y-4">
                      <div className="border-l-4 border-green-500 pl-3">
                        <p className="font-medium text-sm text-green-700 dark:text-green-400">Marketing Digital</p>
                        <p className="text-xs bg-muted p-2 rounded mt-1">
                          "Je suis consultant marketing digital. Aide-moi à créer une stratégie social media pour un restaurant gastronomique à Lyon. Objectif : augmenter la notoriété et les réservations. Format : plan d'action sur 3 mois avec KPIs."
                        </p>
                      </div>
                      <div className="border-l-4 border-blue-500 pl-3">
                        <p className="font-medium text-sm text-blue-700 dark:text-blue-400">Développement</p>
                        <p className="text-xs bg-muted p-2 rounded mt-1">
                          "Je développe une app e-commerce en React. J'ai besoin d'optimiser les performances de mon panier d'achat. Problème : lenteur lors de l'ajout de produits. Tech : React 18, Redux Toolkit. Format : code optimisé avec explications."
                        </p>
                      </div>
                      <div className="border-l-4 border-purple-500 pl-3">
                        <p className="font-medium text-sm text-purple-700 dark:text-purple-400">Business</p>
                        <p className="text-xs bg-muted p-2 rounded mt-1">
                          "Je lance une startup SaaS B2B dans la gestion de projet. Aide-moi à rédiger un pitch deck pour lever 500k€. Cible : business angels français. Format : structure de 12 slides avec points clés."
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
