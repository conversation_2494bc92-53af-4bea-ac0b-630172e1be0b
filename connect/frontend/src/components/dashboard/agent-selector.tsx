'use client';

import React, { useState } from 'react';
import { ChevronDown, Plus, <PERSON>, <PERSON><PERSON>, Edit, User, Brain, Sparkles } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { useAgents } from '@/hooks/react-query/agents/use-agents';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { CreateAgentDialog } from '@/app/(dashboard)/agents/_components/create-agent-dialog';
import { useFeatureFlags } from '@/lib/feature-flags';

interface AgentSelectorProps {
  onAgentSelect?: (agentId: string | undefined) => void;
  selectedAgentId?: string;
  className?: string;
  variant?: 'default' | 'heading' | 'toggle';
}

// Définition des agents avec design style Apple
const AGENT_TYPES = {
  'super-agent': {
    name: 'Super Agent',
    description: 'L\'agent intelligent pour tous vos besoins - Rapide, efficace et polyvalent',
    icon: Brain,
    color: 'green',
    isDefault: true,
    capabilities: ['Polyvalent', 'Rapide', 'Économique']
  },
  'creative-agent': {
    name: 'Agent Créatif',
    description: 'Spécialisé dans la création de contenu, design et innovation',
    icon: Sparkles,
    color: 'orange',
    isDefault: false,
    capabilities: ['Créatif', 'Design', 'Innovation']
  },
  'analyst-agent': {
    name: 'Agent Analyste',
    description: 'Expert en analyse de données et insights business',
    icon: Bot,
    color: 'blue',
    isDefault: false,
    capabilities: ['Analyse', 'Données', 'Business']
  }
};

export function AgentSelector({
  onAgentSelect,
  selectedAgentId,
  className,
  variant = 'default',
}: AgentSelectorProps) {
  const {
    data: agentsResponse,
    isLoading,
    refetch: loadAgents,
  } = useAgents({
    limit: 100,
    sort_by: 'name',
    sort_order: 'asc',
  });

  const { flags, loading: flagsLoading } = useFeatureFlags(['custom_agents']);
  const customAgentsEnabled = flags.custom_agents;

  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  const agents = agentsResponse?.agents || [];
  const defaultAgent = agents.find((agent) => agent.is_default);
  const currentAgent = selectedAgentId
    ? agents.find((agent) => agent.agent_id === selectedAgentId)
    : null;

  // Pour le nouveau design toggle, on utilise 'super-agent' par défaut
  const currentAgentType = selectedAgentId || 'super-agent';
  const displayName = currentAgent?.name || defaultAgent?.name || 'Alex';
  const agentAvatar = currentAgent?.avatar;
  const isUsingSuna = !currentAgent && !defaultAgent;

  const handleAgentSelect = (agentId: string | undefined) => {
    onAgentSelect?.(agentId);
    setIsOpen(false);
  };

  const handleCreateAgent = () => {
    setCreateDialogOpen(true);
    setIsOpen(false);
  };

  const handleManageAgents = () => {
    router.push('/agents');
    setIsOpen(false);
  };

  const handleClearSelection = () => {
    onAgentSelect?.(undefined);
    setIsOpen(false);
  };

  const handleAgentTypeSelect = (agentType: string) => {
    onAgentSelect?.(agentType);
  };

  // Nouveau design toggle style Apple
  if (variant === 'toggle') {
    return (
      <div className={cn('w-full max-w-2xl mx-auto', className)}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {Object.entries(AGENT_TYPES).map(([agentId, agent]) => {
            const isSelected = currentAgentType === agentId;
            const IconComponent = agent.icon;

            return (
              <TooltipProvider key={agentId}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={() => handleAgentTypeSelect(agentId)}
                      className={cn(
                        'relative p-4 rounded-xl border-2 transition-all duration-200 ease-out',
                        'hover:scale-[1.02] active:scale-[0.98]',
                        'focus:outline-none focus:ring-2 focus:ring-offset-2',
                        isSelected && agent.color === 'green' && 'border-green-300 bg-green-50/80 shadow-lg shadow-green-100/50 dark:border-green-700 dark:bg-green-950/50',
                        isSelected && agent.color === 'orange' && 'border-orange-300 bg-orange-50/80 shadow-lg shadow-orange-100/50 dark:border-orange-700 dark:bg-orange-950/50',
                        isSelected && agent.color === 'blue' && 'border-blue-300 bg-blue-50/80 shadow-lg shadow-blue-100/50 dark:border-blue-700 dark:bg-blue-950/50',
                        !isSelected && 'border-gray-200 bg-white/50 hover:border-gray-300 dark:border-gray-700 dark:bg-gray-900/50',
                        'backdrop-blur-sm'
                      )}
                    >
                      <div className="flex flex-col items-center text-center space-y-3">
                        <div className={cn(
                          'p-3 rounded-full transition-colors',
                          isSelected && agent.color === 'green' && 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300',
                          isSelected && agent.color === 'orange' && 'bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300',
                          isSelected && agent.color === 'blue' && 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300',
                          !isSelected && 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
                        )}>
                          <IconComponent className="h-6 w-6" />
                        </div>

                        <div className="space-y-1">
                          <h3 className={cn(
                            'font-semibold text-sm transition-colors',
                            isSelected ? 'text-gray-900 dark:text-gray-100' : 'text-gray-700 dark:text-gray-300'
                          )}>
                            {agent.name}
                          </h3>
                          <p className={cn(
                            'text-xs leading-relaxed transition-colors',
                            isSelected ? 'text-gray-600 dark:text-gray-400' : 'text-gray-500 dark:text-gray-500'
                          )}>
                            {agent.description}
                          </p>
                        </div>

                        {agent.isDefault && (
                          <Badge
                            variant="outline"
                            className={cn(
                              'text-xs px-2 py-0.5 transition-colors',
                              isSelected && agent.color === 'green' && 'border-green-300 text-green-700 bg-green-50 dark:border-green-700 dark:text-green-300 dark:bg-green-950',
                              !isSelected && 'border-gray-300 text-gray-600 bg-gray-50 dark:border-gray-600 dark:text-gray-400 dark:bg-gray-800'
                            )}
                          >
                            Recommandé
                          </Badge>
                        )}
                      </div>

                      {isSelected && (
                        <div className={cn(
                          'absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white shadow-sm',
                          agent.color === 'green' && 'bg-green-500',
                          agent.color === 'orange' && 'bg-orange-500',
                          agent.color === 'blue' && 'bg-blue-500'
                        )} />
                      )}
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="max-w-xs">
                    <div className="space-y-2">
                      <p className="font-medium">{agent.name}</p>
                      <p className="text-sm text-muted-foreground">{agent.description}</p>
                      <div className="flex flex-wrap gap-1">
                        {agent.capabilities.map((capability) => (
                          <Badge key={capability} variant="secondary" className="text-xs">
                            {capability}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            );
          })}
        </div>
      </div>
    );
  }

  if (!customAgentsEnabled) {
    if (variant === 'heading') {
      return (
        <div className={cn('flex items-center', className)}>
          <span className="tracking-tight text-4xl font-semibold leading-tight text-primary">
            Alex
          </span>
        </div>
      );
    }
  }

  if (isLoading) {
    if (variant === 'heading') {
      return (
        <div className={cn('flex items-center', className)}>
          <span className="tracking-tight text-4xl font-semibold leading-tight text-muted-foreground">
            Loading...
          </span>
        </div>
      );
    }

    return (
      <div className={cn('flex items-center gap-2', className)}>
        <div className="flex items-center gap-2 px-3 py-2 rounded-lg border bg-background">
          <Bot className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">
            Loading agents...
          </span>
        </div>
      </div>
    );
  }

  if (variant === 'heading') {
    return (
      <>
        <div className={cn('flex items-center', className)}>
          <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex items-center gap-1 px-2 py-1 h-auto hover:bg-transparent hover:text-primary transition-colors group"
              >
                <span className="underline decoration-dashed underline-offset-6 decoration-muted-foreground/50 tracking-tight text-4xl font-semibold leading-tight text-primary">
                  {displayName}
                  <span className="text-muted-foreground ml-2">
                    {agentAvatar && agentAvatar}
                  </span>
                </span>
                <div className="flex items-center opacity-60 group-hover:opacity-100 transition-opacity">
                  <ChevronDown className="h-5 w-5 text-muted-foreground" />
                  <Edit className="h-4 w-4 text-muted-foreground ml-1" />
                </div>
              </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent align="start" className="w-[320px]">
              <div className="px-3 py-2">
                <p className="text-sm font-medium">Select an agent</p>
                <p className="text-xs text-muted-foreground">
                  You can create your own agent
                </p>
              </div>

              <DropdownMenuSeparator />

              <DropdownMenuItem
                onClick={() => handleClearSelection()}
                className="flex flex-col items-start gap-1 p-3 cursor-pointer"
              >
                <div className="flex items-center gap-2 w-full">
                  <User className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <div className="flex items-center gap-1 flex-1 min-w-0">
                    <span className="font-medium truncate">Alex</span>
                    <Badge
                      variant="outline"
                      className="text-xs px-1 py-0 flex-shrink-0"
                    >
                      Default
                    </Badge>
                  </div>
                  {isUsingSuna && (
                    <div className="h-2 w-2 rounded-full bg-primary flex-shrink-0" />
                  )}
                </div>
                <span className="text-xs text-muted-foreground pl-6 line-clamp-2">
                  Your personal AI employee
                </span>
              </DropdownMenuItem>
              {agents.length > 0 ? (
                <>
                  {agents.map((agent) => (
                    <DropdownMenuItem
                      key={agent.agent_id}
                      onClick={() => handleAgentSelect(agent.agent_id)}
                      className="flex flex-col items-start gap-1 p-3 cursor-pointer"
                    >
                      <div className="flex items-center gap-2 w-full">
                        {agent.avatar}
                        <div className="flex items-center gap-1 flex-1 min-w-0">
                          <span className="font-medium truncate">
                            {agent.name}
                          </span>
                          {agent.is_default && (
                            <Badge
                              variant="secondary"
                              className="text-xs px-1 py-0 flex-shrink-0"
                            >
                              <Star className="h-2.5 w-2.5 mr-0.5 fill-current" />
                              System
                            </Badge>
                          )}
                        </div>
                        {currentAgent?.agent_id === agent.agent_id && (
                          <div className="h-2 w-2 rounded-full bg-primary flex-shrink-0" />
                        )}
                      </div>
                      {agent.description && (
                        <span className="text-xs text-muted-foreground pl-6 line-clamp-2">
                          {agent.description}
                        </span>
                      )}
                    </DropdownMenuItem>
                  ))}
                </>
              ) : null}

              <DropdownMenuSeparator />

              <DropdownMenuItem
                onClick={handleCreateAgent}
                className="cursor-pointer"
              >
                Agent Playground
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </>
    );
  }

  return (
    <>
      <div className={cn('flex items-center gap-2', className)}>
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="flex items-center gap-2 px-3 py-2 h-auto min-w-[200px] justify-between"
            >
              <div className="flex items-center gap-2">
                {isUsingSuna ? (
                  <User className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <Bot className="h-4 w-4 text-muted-foreground" />
                )}
                <div className="flex flex-col items-start">
                  <div className="flex items-center gap-1">
                    <span className="text-sm font-medium">{displayName}</span>
                    {isUsingSuna && (
                      <Badge variant="outline" className="text-xs px-1 py-0">
                        Default
                      </Badge>
                    )}
                    {currentAgent?.is_default && (
                      <Badge variant="secondary" className="text-xs px-1 py-0">
                        <Star className="h-2.5 w-2.5 mr-0.5 fill-current" />
                        System
                      </Badge>
                    )}
                  </div>
                  {currentAgent?.description ? (
                    <span className="text-xs text-muted-foreground line-clamp-1 max-w-[150px]">
                      {currentAgent.description}
                    </span>
                  ) : isUsingSuna ? (
                    <span className="text-xs text-muted-foreground line-clamp-1 max-w-[150px]">
                      Your personal AI employee
                    </span>
                  ) : null}
                </div>
              </div>
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            </Button>
          </DropdownMenuTrigger>

          <DropdownMenuContent align="start" className="w-[280px]">
            <DropdownMenuItem
              onClick={() => handleClearSelection()}
              className="flex flex-col items-start gap-1 p-3 cursor-pointer"
            >
              <div className="flex items-center gap-2 w-full">
                <User className="h-4 w-4 text-muted-foreground" />
                <div className="flex items-center gap-1 flex-1">
                  <span className="font-medium">Alex</span>
                  <Badge variant="outline" className="text-xs px-1 py-0">
                    Default
                  </Badge>
                </div>
                {isUsingSuna && (
                  <div className="h-2 w-2 rounded-full bg-primary" />
                )}
              </div>
              <span className="text-xs text-muted-foreground pl-6 line-clamp-2">
                Your personal AI employee
              </span>
            </DropdownMenuItem>
            {agents.length > 0 ? (
              <>
                {agents.map((agent) => (
                  <DropdownMenuItem
                    key={agent.agent_id}
                    onClick={() => handleAgentSelect(agent.agent_id)}
                    className="flex flex-col items-start gap-1 p-3 cursor-pointer"
                  >
                    <div className="flex items-center gap-2 w-full">
                      <Bot className="h-4 w-4 text-muted-foreground" />
                      <div className="flex items-center gap-1 flex-1">
                        <span className="font-medium">{agent.name}</span>
                        {agent.is_default && (
                          <Badge
                            variant="secondary"
                            className="text-xs px-1 py-0"
                          >
                            <Star className="h-2.5 w-2.5 mr-0.5 fill-current" />
                            System
                          </Badge>
                        )}
                      </div>
                      {currentAgent?.agent_id === agent.agent_id && (
                        <div className="h-2 w-2 rounded-full bg-primary" />
                      )}
                    </div>
                    {agent.description && (
                      <span className="text-xs text-muted-foreground pl-6 line-clamp-2">
                        {agent.description}
                      </span>
                    )}
                  </DropdownMenuItem>
                ))}
              </>
            ) : null}

            <DropdownMenuSeparator />

            <DropdownMenuItem
              onClick={handleCreateAgent}
              className="cursor-pointer"
            >
              <Plus className="h-4 w-4" />
              Create New Agent
            </DropdownMenuItem>

            <DropdownMenuItem
              onClick={handleManageAgents}
              className="cursor-pointer"
            >
              <Bot className="h-4 w-4" />
              Manage All Agents
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <CreateAgentDialog
        isOpen={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onAgentCreated={loadAgents}
      />
    </>
  );
}
