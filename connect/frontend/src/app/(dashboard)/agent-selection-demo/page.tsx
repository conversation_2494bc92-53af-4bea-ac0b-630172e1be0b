'use client';

import React, { useState } from 'react';
import { AgentSelector } from '@/components/dashboard/agent-selector';

export default function AgentSelectionDemo() {
  const [selectedAgent, setSelectedAgent] = useState<string | undefined>('super-agent');

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">
            Choisissez votre Agent
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Sélectionnez l'agent qui correspond le mieux à vos besoins. 
            Chaque agent est optimisé pour des tâches spécifiques.
          </p>
        </div>

        <div className="bg-white/70 dark:bg-gray-900/70 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-gray-200/50 dark:border-gray-700/50">
          <AgentSelector
            variant="toggle"
            selectedAgentId={selectedAgent}
            onAgentSelect={setSelectedAgent}
            className="w-full"
          />
        </div>

        {selectedAgent && (
          <div className="text-center p-6 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-200/50 dark:border-gray-700/50">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Agent sélectionné : <span className="font-semibold text-gray-900 dark:text-gray-100">{selectedAgent}</span>
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
          <div className="text-center space-y-2">
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
              <span className="text-green-600 dark:text-green-400 text-xl">🚀</span>
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100">Ultra Rapide</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Réponses instantanées pour tous vos besoins
            </p>
          </div>
          
          <div className="text-center space-y-2">
            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mx-auto">
              <span className="text-orange-600 dark:text-orange-400 text-xl">✨</span>
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100">Design Épuré</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Interface pensée pour une expérience optimale
            </p>
          </div>
          
          <div className="text-center space-y-2">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto">
              <span className="text-blue-600 dark:text-blue-400 text-xl">🎯</span>
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100">Spécialisé</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Chaque agent optimisé pour ses domaines
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
