'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Mail, 
  MessageSquare, 
  ShoppingCart, 
  BarChart3, 
  Calendar,
  FileText,
  Database,
  Zap,
  CheckCircle,
  Clock,
  Plus
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  status: 'available' | 'connected' | 'coming_soon';
  popular?: boolean;
}

const INTEGRATION_PACKS: Integration[] = [
  // Pack Email
  {
    id: 'gmail',
    name: 'Gmail',
    description: 'Gérez vos emails automatiquement',
    icon: <Mail className="h-6 w-6" />,
    category: 'Email',
    status: 'available',
    popular: true
  },
  {
    id: 'outlook',
    name: 'Outlook',
    description: 'Intégration Microsoft Outlook',
    icon: <Mail className="h-6 w-6" />,
    category: 'Email',
    status: 'coming_soon'
  },
  
  // Pack Communication
  {
    id: 'slack',
    name: 'Slack',
    description: 'Intégrez avec votre équipe',
    icon: <MessageSquare className="h-6 w-6" />,
    category: 'Communication',
    status: 'available',
    popular: true
  },
  {
    id: 'teams',
    name: 'Microsoft Teams',
    description: 'Collaboration d\'équipe',
    icon: <MessageSquare className="h-6 w-6" />,
    category: 'Communication',
    status: 'coming_soon'
  },
  
  // Pack E-commerce
  {
    id: 'shopify',
    name: 'Shopify',
    description: 'Gérez votre boutique en ligne',
    icon: <ShoppingCart className="h-6 w-6" />,
    category: 'E-commerce',
    status: 'available'
  },
  {
    id: 'woocommerce',
    name: 'WooCommerce',
    description: 'Intégration WordPress',
    icon: <ShoppingCart className="h-6 w-6" />,
    category: 'E-commerce',
    status: 'coming_soon'
  },
  
  // Pack Analytics
  {
    id: 'google-analytics',
    name: 'Google Analytics',
    description: 'Analysez vos données web',
    icon: <BarChart3 className="h-6 w-6" />,
    category: 'Analytics',
    status: 'available'
  },
  {
    id: 'hubspot',
    name: 'HubSpot',
    description: 'CRM et marketing automation',
    icon: <BarChart3 className="h-6 w-6" />,
    category: 'Analytics',
    status: 'available',
    popular: true
  },
  
  // Pack Productivité
  {
    id: 'google-calendar',
    name: 'Google Calendar',
    description: 'Gérez vos rendez-vous',
    icon: <Calendar className="h-6 w-6" />,
    category: 'Productivité',
    status: 'available'
  },
  {
    id: 'notion',
    name: 'Notion',
    description: 'Workspace tout-en-un',
    icon: <FileText className="h-6 w-6" />,
    category: 'Productivité',
    status: 'coming_soon'
  },
  
  // Pack Base de données
  {
    id: 'airtable',
    name: 'Airtable',
    description: 'Base de données collaborative',
    icon: <Database className="h-6 w-6" />,
    category: 'Base de données',
    status: 'available'
  },
  {
    id: 'mysql',
    name: 'MySQL',
    description: 'Base de données relationnelle',
    icon: <Database className="h-6 w-6" />,
    category: 'Base de données',
    status: 'coming_soon'
  }
];

const CATEGORIES = ['Tous', 'Email', 'Communication', 'E-commerce', 'Analytics', 'Productivité', 'Base de données'];

export default function IntegrationsPage() {
  const [selectedCategory, setSelectedCategory] = useState('Tous');
  const [connectedIntegrations, setConnectedIntegrations] = useState<Set<string>>(new Set());

  const filteredIntegrations = INTEGRATION_PACKS.filter(integration => 
    selectedCategory === 'Tous' || integration.category === selectedCategory
  );

  const popularIntegrations = INTEGRATION_PACKS.filter(integration => integration.popular);

  const handleConnect = (integrationId: string) => {
    // Pour l'instant, on simule juste la connexion
    setConnectedIntegrations(prev => new Set([...prev, integrationId]));
  };

  const getStatusBadge = (status: Integration['status'], integrationId: string) => {
    if (connectedIntegrations.has(integrationId)) {
      return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Connecté</Badge>;
    }
    
    switch (status) {
      case 'available':
        return <Badge variant="outline">Disponible</Badge>;
      case 'connected':
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Connecté</Badge>;
      case 'coming_soon':
        return <Badge variant="secondary"><Clock className="h-3 w-3 mr-1" />Bientôt</Badge>;
    }
  };

  const getActionButton = (integration: Integration) => {
    if (connectedIntegrations.has(integration.id)) {
      return <Button variant="outline" size="sm">Gérer</Button>;
    }
    
    switch (integration.status) {
      case 'available':
        return (
          <Button 
            size="sm" 
            onClick={() => handleConnect(integration.id)}
            className="bg-primary hover:bg-primary/90"
          >
            <Plus className="h-4 w-4 mr-1" />
            Connecter
          </Button>
        );
      case 'coming_soon':
        return <Button variant="ghost" size="sm" disabled>Bientôt disponible</Button>;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Intégrations</h1>
        <p className="text-muted-foreground mt-2">
          Connectez Orchestra Connect avec vos outils favoris pour une expérience optimale.
        </p>
      </div>

      {/* Intégrations populaires */}
      <div>
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Zap className="h-5 w-5 mr-2 text-yellow-500" />
          Intégrations populaires
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {popularIntegrations.map((integration) => (
            <Card key={integration.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      {integration.icon}
                    </div>
                    <div>
                      <CardTitle className="text-base">{integration.name}</CardTitle>
                      <CardDescription className="text-sm">{integration.description}</CardDescription>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                  {getStatusBadge(integration.status, integration.id)}
                  {getActionButton(integration)}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <Separator />

      {/* Filtres par catégorie */}
      <div>
        <div className="flex flex-wrap gap-2 mb-6">
          {CATEGORIES.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Toutes les intégrations */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredIntegrations.map((integration) => (
            <Card key={integration.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      {integration.icon}
                    </div>
                    <div>
                      <CardTitle className="text-base flex items-center">
                        {integration.name}
                        {integration.popular && (
                          <Badge variant="secondary" className="ml-2 text-xs">Populaire</Badge>
                        )}
                      </CardTitle>
                      <CardDescription className="text-sm">{integration.description}</CardDescription>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                  {getStatusBadge(integration.status, integration.id)}
                  {getActionButton(integration)}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
