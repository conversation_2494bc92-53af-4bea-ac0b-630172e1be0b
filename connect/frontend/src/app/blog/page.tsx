'use client';

import { FooterSection } from '@/components/home/<USER>/footer-section';
import AuroraBackground from '@/components/home/<USER>/AuroraBackground';
import { ModalProviders } from '@/providers/modal-providers';
import Link from 'next/link';
import Image from 'next/image';
import { ChevronLeftIcon } from '@radix-ui/react-icons';

// Les métadonnées sont définies dans un fichier séparé (metadata.ts)

export default function BlogPage() {
  return (
    <>
      <AuroraBackground />
      <ModalProviders />
      <main className="flex flex-col items-center justify-center min-h-screen w-full">
        <div className="w-full max-w-6xl mx-auto pt-24 px-4 sm:px-6">
          <div className="flex items-center mb-6">
            <Link
              href="/"
              className="flex items-center text-primary hover:underline"
            >
              <ChevronLeftIcon className="mr-1" />
              Retour à l&apos;accueil
            </Link>
          </div>

          <h1 className="text-4xl font-bold mb-8 text-center bg-clip-text text-transparent bg-gradient-to-r from-green-400 to-blue-500">
            Blog Orchestra Connect : L&apos;IA au Service de Votre PME
          </h1>

          <div className="text-center mb-12">
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Découvrez comment l&apos;Intelligence Artificielle et notre
              super-agent Alex peuvent transformer votre quotidien
              d&apos;entrepreneur, optimiser vos opérations et accélérer la
              croissance de votre PME.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            <div className="group bg-card rounded-xl overflow-hidden border border-border hover:border-primary transition-all shadow-sm hover:shadow-md">
              <div className="relative h-60 w-full overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                  alt="L'IA pour les PME"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6">
                <span className="text-xs text-muted-foreground">
                  28 mai 2025
                </span>
                <h2 className="text-2xl font-semibold mt-2 mb-3 group-hover:text-primary transition-colors">
                  L&apos;IA pour les PME : Un Levier de Croissance
                  Incontournable en 2025
                </h2>
                <p className="text-muted-foreground mb-4 line-clamp-3">
                  Découvrez comment l&apos;Intelligence Artificielle (IA)
                  devient un levier de croissance essentiel pour les PME en
                  2025. Optimisez, innovez et gagnez en productivité.
                </p>
                <Link
                  href="/blog/ia-pme-croissance-2025"
                  className="text-primary hover:underline font-medium inline-flex items-center"
                >
                  Lire l&apos;article complet
                  <svg
                    className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </Link>
              </div>
            </div>

            {/* Article sur l'optimisation des processus */}
            <div className="group bg-card rounded-xl overflow-hidden border border-border hover:border-primary transition-all shadow-sm hover:shadow-md">
              <div className="relative h-60 w-full overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2015&q=80"
                  alt="Optimisation des processus métiers"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6">
                <span className="text-xs text-muted-foreground">
                  30 mai 2025
                </span>
                <h2 className="text-2xl font-semibold mt-2 mb-3 group-hover:text-primary transition-colors">
                  Optimisez vos processus métiers grâce à l&apos;agent IA Alex
                </h2>
                <p className="text-muted-foreground mb-4 line-clamp-3">
                  Dans un monde professionnel où le temps est précieux,
                  découvrez comment Alex révolutionne la gestion de vos
                  processus métiers quotidiens et libère le potentiel de vos
                  équipes.
                </p>
                <Link
                  href="/blog/optimiser-processus-metiers-ia-alex"
                  className="text-primary hover:underline font-medium inline-flex items-center"
                >
                  Lire l&apos;article complet
                  <svg
                    className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </Link>
              </div>
            </div>

            {/* Article sur la sécurité des données */}
            <div className="group bg-card rounded-xl overflow-hidden border border-border hover:border-primary transition-all shadow-sm hover:shadow-md">
              <div className="relative h-60 w-full overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                  alt="Sécurité des données et IA"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6">
                <span className="text-xs text-muted-foreground">
                  5 juin 2025
                </span>
                <h2 className="text-2xl font-semibold mt-2 mb-3 group-hover:text-primary transition-colors">
                  Sécurité des données : un enjeu majeur pour les PME à
                  l&apos;ère de l&apos;IA
                </h2>
                <p className="text-muted-foreground mb-4 line-clamp-3">
                  À l&apos;heure où l&apos;Intelligence Artificielle transforme
                  les PME, découvrez comment protéger efficacement votre capital
                  informationnel tout en exploitant le potentiel de l&apos;IA.
                </p>
                <Link
                  href="/blog/securite-donnees-pme-ia"
                  className="text-primary hover:underline font-medium inline-flex items-center"
                >
                  Lire l&apos;article complet
                  <svg
                    className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </Link>
              </div>
            </div>

            {/* Article sur l'IA et l'écologie */}
            <div className="group bg-card rounded-xl overflow-hidden border border-border hover:border-primary transition-all shadow-sm hover:shadow-md">
              <div className="relative h-60 w-full overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80"
                  alt="IA et écologie"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <div className="p-6">
                <span className="text-xs text-muted-foreground">
                  12 juin 2025
                </span>
                <h2 className="text-2xl font-semibold mt-2 mb-3 group-hover:text-primary transition-colors">
                  IA et écologie : comment la technologie peut servir le
                  développement durable
                </h2>
                <p className="text-muted-foreground mb-4 line-clamp-3">
                  Explorez comment l&apos;intelligence artificielle peut aider
                  les PME à réduire leur impact environnemental tout en
                  optimisant leurs performances économiques.
                </p>
                <Link
                  href="/blog/ia-ecologie-developpement-durable"
                  className="text-primary hover:underline font-medium inline-flex items-center"
                >
                  Lire l&apos;article complet
                  <svg
                    className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </Link>
              </div>
            </div>
          </div>

          <div className="text-center mb-16">
            <h2 className="text-2xl font-semibold mb-3">
              Vous souhaitez recevoir nos articles ?
            </h2>
            <p className="text-muted-foreground mb-6">
              Inscrivez-vous à notre newsletter pour ne manquer aucune
              publication
            </p>
            <div className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Votre adresse email"
                className="flex-1 px-4 py-2 rounded-md border border-input bg-background text-foreground"
              />

              <button className="bg-primary text-white font-medium px-6 py-2 rounded-md hover:bg-primary/90 transition-colors">
                S&apos;inscrire
              </button>
            </div>
          </div>
        </div>

        <FooterSection />
      </main>
    </>
  );
}
