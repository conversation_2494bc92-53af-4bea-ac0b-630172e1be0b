# 🔧 VARIABLES D'ENVIRONNEMENT RENDER - CONNECT PROJECT

## 📋 CONFIGURATION COMPLÈTE PAR SERVICE

### **🎯 Frontend (connect-frontend)**
```bash
# Supabase (Public - Safe to expose)
NEXT_PUBLIC_SUPABASE_URL=https://irfvjdismpsxwihifsel.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.S_KVhio9_2VpGZPgIieAf22TYeNu3qyUns6HyERqYpI

# Optional
NEXT_PUBLIC_GOOGLE_CLIENT_ID=
NEXT_PUBLIC_SENTRY_DSN=
```

### **🎯 Backend (connect-backend)**
```bash
# Environment
ENV_MODE=production

# Supabase
SUPABASE_URL=https://irfvjdismpsxwihifsel.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.S_KVhio9_2VpGZPgIieAf22TYeNu3qyUns6HyERqYpI
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.Z1k2HbdP-Z1m5mvqRw7ynWrkAPKUTJ11_8bzdDHxAM4

# LLM Providers (NOUVELLES CLÉS REQUISES)
ANTHROPIC_API_KEY=sk-ant-api03-VOTRE_NOUVELLE_CLE_ANTHROPIC
OPENAI_API_KEY=sk-VOTRE_NOUVELLE_CLE_OPENAI
OPENROUTER_API_KEY=sk-or-v1-VOTRE_NOUVELLE_CLE_OPENROUTER
MODEL_TO_USE=claude-3-5-sonnet-20241022
GROQ_API_KEY=

# Data APIs (NOUVELLES CLÉS REQUISES)
RAPID_API_KEY=VOTRE_NOUVELLE_CLE_RAPIDAPI

# Web Search (NOUVELLE CLÉ REQUISE)
TAVILY_API_KEY=tvly-VOTRE_NOUVELLE_CLE_TAVILY

# Web Scraping (NOUVELLE CLÉ REQUISE)
FIRECRAWL_API_KEY=fc-VOTRE_NOUVELLE_CLE_FIRECRAWL
FIRECRAWL_URL=https://api.firecrawl.dev

# Sandbox Provider (NOUVELLE CLÉ REQUISE)
DAYTONA_API_KEY=dtn_VOTRE_NOUVELLE_CLE_DAYTONA
DAYTONA_SERVER_URL=https://app.daytona.io/api
DAYTONA_TARGET=us

# Stripe (NOUVELLES CLÉS REQUISES)
STRIPE_SECRET_KEY=sk_live_VOTRE_NOUVELLE_CLE_STRIPE
STRIPE_WEBHOOK_SECRET=whsec_VOTRE_NOUVEAU_SECRET_WEBHOOK

# Email Service
MAILTRAP_API_TOKEN=VOTRE_TOKEN_MAILTRAP
MAILTRAP_SENDER_EMAIL=<EMAIL>
MAILTRAP_SENDER_NAME=Orchestra Connect

# AWS (Optional)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION_NAME=

# Monitoring (Optional)
LANGFUSE_PUBLIC_KEY=
LANGFUSE_SECRET_KEY=
LANGFUSE_HOST=https://cloud.langfuse.com
SMITHERY_API_KEY=
```

### **🎯 Worker (connect-worker)**
```bash
# Environment & Process
ENV_MODE=production
DRAMATIQ_THREADS=2
DRAMATIQ_PROCESSES=1
PYTHONDONTWRITEBYTECODE=1
PYTHONUNBUFFERED=1

# Supabase (Same as Backend)
SUPABASE_URL=https://irfvjdismpsxwihifsel.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.S_KVhio9_2VpGZPgIieAf22TYeNu3qyUns6HyERqYpI
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.Z1k2HbdP-Z1m5mvqRw7ynWrkAPKUTJ11_8bzdDHxAM4

# All API Keys (Same as Backend)
ANTHROPIC_API_KEY=sk-ant-api03-VOTRE_NOUVELLE_CLE_ANTHROPIC
OPENAI_API_KEY=sk-VOTRE_NOUVELLE_CLE_OPENAI
OPENROUTER_API_KEY=sk-or-v1-VOTRE_NOUVELLE_CLE_OPENROUTER
MODEL_TO_USE=openrouter/google/gemini-2.5-flash-preview-05-20
RAPID_API_KEY=VOTRE_NOUVELLE_CLE_RAPIDAPI
TAVILY_API_KEY=tvly-VOTRE_NOUVELLE_CLE_TAVILY
FIRECRAWL_API_KEY=fc-VOTRE_NOUVELLE_CLE_FIRECRAWL
FIRECRAWL_URL=https://api.firecrawl.dev
DAYTONA_API_KEY=dtn_VOTRE_NOUVELLE_CLE_DAYTONA
DAYTONA_SERVER_URL=https://app.daytona.io/api
DAYTONA_TARGET=us
STRIPE_SECRET_KEY=sk_live_VOTRE_NOUVELLE_CLE_STRIPE
STRIPE_WEBHOOK_SECRET=whsec_VOTRE_NOUVEAU_SECRET_WEBHOOK
MAILTRAP_API_TOKEN=VOTRE_TOKEN_MAILTRAP
MAILTRAP_SENDER_EMAIL=<EMAIL>
MAILTRAP_SENDER_NAME=Orchestra Connect
LANGFUSE_HOST=https://cloud.langfuse.com
LANGFUSE_PUBLIC_KEY=
LANGFUSE_SECRET_KEY=
```

### **🎯 RabbitMQ (connect-rabbitmq)**
```bash
# RabbitMQ utilise la configuration par défaut
# Pas de variables supplémentaires nécessaires
RABBITMQ_URL=amqp://guest:guest@localhost:5672
```

## 🔧 **REDIS - Configuration Confirmée**

Redis utilise **Render Managed Redis** - Configuration automatique via render.yaml :
- Host: `red-d197f5ffte5s73c39cjg`
- Port: `6379`
- SSL: `false`
- URL: `redis://red-d197f5ffte5s73c39cjg:6379`

**Pas de variables supplémentaires nécessaires pour Redis.**

## 📝 **INSTRUCTIONS DE CONFIGURATION**

### 1. Accéder à Render Dashboard
- https://dashboard.render.com/
- Sélectionner votre service
- Aller dans "Environment"

### 2. Ajouter les variables
- Cliquer "Add Environment Variable"
- Copier-coller chaque variable
- Sauvegarder

### 3. Redéployer
- Le redéploiement se fait automatiquement
- Vérifier les logs de déploiement

## 🚨 **CLÉS À RENOUVELER IMMÉDIATEMENT**

1. **Stripe**: https://dashboard.stripe.com/apikeys
2. **OpenRouter**: https://openrouter.ai/keys
3. **RapidAPI**: https://rapidapi.com/developer/security
4. **Tavily**: https://app.tavily.com/api-keys
5. **Firecrawl**: https://firecrawl.dev/app/api-keys
6. **Daytona**: https://app.daytona.io/settings/api-keys
7. **OpenAI**: https://platform.openai.com/api-keys
8. **Anthropic**: https://console.anthropic.com/settings/keys
