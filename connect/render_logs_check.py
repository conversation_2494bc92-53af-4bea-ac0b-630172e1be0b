#!/usr/bin/env python3
"""
Script pour récupérer les logs Render via l'API
"""

import requests
import json
import sys
from datetime import datetime, timedelta

# Token Render
RENDER_TOKEN = "rnd_TZ7rcqWJoVwq9zMdzpXpacLZxMxs"
RENDER_API_BASE = "https://api.render.com/v1"

def get_services():
    """Récupère la liste des services"""
    headers = {
        "Authorization": f"Bearer {RENDER_TOKEN}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(f"{RENDER_API_BASE}/services", headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Erreur lors de la récupération des services: {response.status_code}")
        print(response.text)
        return None

def get_service_logs(service_id, limit=100):
    """Récupère les logs d'un service"""
    headers = {
        "Authorization": f"Bearer {RENDER_TOKEN}",
        "Content-Type": "application/json"
    }

    # Récupérer les logs des dernières heures
    end_time = datetime.utcnow()
    start_time = end_time - timedelta(hours=2)

    params = {
        "limit": limit,
        "startTime": start_time.isoformat() + "Z",
        "endTime": end_time.isoformat() + "Z"
        # Note: filtrage par service sera fait après récupération
    }

    response = requests.get(
        f"{RENDER_API_BASE}/logs",
        headers=headers,
        params=params
    )

    if response.status_code == 200:
        return response.json()
    else:
        print(f"Erreur lors de la récupération des logs: {response.status_code}")
        print(response.text)
        return None

def main():
    print("🔍 Récupération des services Render...")

    services_response = get_services()
    if not services_response:
        sys.exit(1)

    # La réponse peut être une liste ou un dict avec une clé 'services'
    if isinstance(services_response, list):
        services = services_response
    elif isinstance(services_response, dict) and 'services' in services_response:
        services = services_response['services']
    else:
        print("Structure de réponse inattendue:")
        print(json.dumps(services_response, indent=2))
        sys.exit(1)

    print(f"✅ {len(services)} services trouvés:")

    backend_service = None
    for service in services:
        service_name = service.get('name', service.get('service', {}).get('name', 'Unknown'))
        service_id = service.get('id', service.get('service', {}).get('id', 'Unknown'))
        service_type = service.get('type', service.get('service', {}).get('type', 'Unknown'))

        print(f"- {service_name} ({service_id}) - {service_type}")
        if 'connect-backend' in service_name.lower():
            backend_service = service
    
    if not backend_service:
        print("❌ Service backend non trouvé")
        print("Services disponibles:")
        for service in services:
            print(f"  - {service['name']} ({service['id']})")
        return
    
    backend_name = backend_service.get('name', backend_service.get('service', {}).get('name', 'Unknown'))
    backend_id = backend_service.get('id', backend_service.get('service', {}).get('id', 'Unknown'))

    print(f"\n🔍 Récupération des logs du service backend: {backend_name}")

    logs = get_service_logs(backend_id)
    if not logs:
        return
    
    print(f"✅ {len(logs)} entrées de log récupérées")
    
    # Filtrer les logs liés au billing et aux erreurs 403
    relevant_logs = []
    for log in logs:
        message = log.get('message', '').lower()
        if any(keyword in message for keyword in [
            'debug', 'billing', 'subscription', 'tier', 'model', 'stripe', 
            '403', 'forbidden', 'can_use_model', 'allowed_models'
        ]):
            relevant_logs.append(log)
    
    print(f"\n📋 {len(relevant_logs)} logs pertinents trouvés:")
    print("=" * 80)
    
    for log in relevant_logs[-20:]:  # Derniers 20 logs pertinents
        timestamp = log.get('timestamp', 'N/A')
        message = log.get('message', 'N/A')
        level = log.get('level', 'INFO')
        
        print(f"[{timestamp}] {level}: {message}")
    
    # Sauvegarder tous les logs
    with open('render_logs_full.json', 'w') as f:
        json.dump(logs, f, indent=2)
    
    with open('render_logs_relevant.json', 'w') as f:
        json.dump(relevant_logs, f, indent=2)
    
    print(f"\n📄 Logs sauvegardés:")
    print(f"- render_logs_full.json ({len(logs)} entrées)")
    print(f"- render_logs_relevant.json ({len(relevant_logs)} entrées)")

if __name__ == "__main__":
    main()
