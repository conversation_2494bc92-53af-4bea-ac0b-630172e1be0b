#!/usr/bin/env python3
"""
Script pour analyser les logs Render et diagnostiquer le problème 403
"""

import requests
import json
import sys
from datetime import datetime, timedelta

# Configuration
RENDER_TOKEN = "rnd_TZ7rcqWJoVwq9zMdzpXpacLZxMxs"
RENDER_API_BASE = "https://api.render.com/v1"
OWNER_ID = "tea-d0urtvidbo4c73c3np1g"
BACKEND_SERVICE_ID = "srv-d18slh15pdvs73ctt1i0"

def get_logs(limit=50, start_time=None, end_time=None):
    """Récupère les logs du service backend"""
    headers = {
        "Authorization": f"Bearer {RENDER_TOKEN}",
        "Content-Type": "application/json"
    }
    
    params = {
        "ownerId": OWNER_ID,
        "resource": BACKEND_SERVICE_ID,
        "limit": limit
    }
    
    if start_time:
        params["startTime"] = start_time
    if end_time:
        params["endTime"] = end_time
    
    response = requests.get(f"{RENDER_API_BASE}/logs", headers=headers, params=params)
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Erreur lors de la récupération des logs: {response.status_code}")
        print(response.text)
        return None

def analyze_logs():
    """Analyse les logs pour diagnostiquer le problème 403"""
    print("🔍 Analyse des logs pour diagnostiquer le problème 403...")
    
    # Récupérer les logs récents
    logs_data = get_logs(limit=50)
    if not logs_data or 'logs' not in logs_data:
        print("❌ Impossible de récupérer les logs")
        return
    
    logs = logs_data['logs']
    print(f"✅ {len(logs)} logs récupérés")
    
    # Analyser les logs
    error_403_logs = []
    billing_logs = []
    debug_logs = []
    agent_initiate_logs = []
    
    for log in logs:
        message = log.get('message', '').lower()
        
        # Logs d'erreur 403
        if '403' in message:
            error_403_logs.append(log)
        
        # Logs de billing/subscription
        if any(keyword in message for keyword in ['billing', 'subscription', 'tier', 'stripe', 'can_use_model', 'allowed_models']):
            billing_logs.append(log)
        
        # Logs de debug
        if 'debug' in message:
            debug_logs.append(log)
        
        # Logs d'initiation d'agent
        if 'agent/initiate' in message:
            agent_initiate_logs.append(log)
    
    print(f"\n📊 RÉSULTATS DE L'ANALYSE:")
    print(f"- Erreurs 403: {len(error_403_logs)}")
    print(f"- Logs de billing: {len(billing_logs)}")
    print(f"- Logs de debug: {len(debug_logs)}")
    print(f"- Logs d'initiation d'agent: {len(agent_initiate_logs)}")
    
    # Afficher les erreurs 403
    if error_403_logs:
        print(f"\n❌ ERREURS 403 DÉTECTÉES:")
        for log in error_403_logs[-5:]:  # Dernières 5 erreurs
            timestamp = log.get('timestamp', 'N/A')
            message = log.get('message', 'N/A')
            print(f"[{timestamp}] {message}")
    
    # Afficher les logs de billing
    if billing_logs:
        print(f"\n💰 LOGS DE BILLING:")
        for log in billing_logs[-5:]:  # Derniers 5 logs
            timestamp = log.get('timestamp', 'N/A')
            message = log.get('message', 'N/A')
            print(f"[{timestamp}] {message}")
    
    # Afficher les logs de debug
    if debug_logs:
        print(f"\n🐛 LOGS DE DEBUG:")
        for log in debug_logs[-5:]:  # Derniers 5 logs
            timestamp = log.get('timestamp', 'N/A')
            message = log.get('message', 'N/A')
            print(f"[{timestamp}] {message}")
    
    # Si pas de logs de debug/billing, essayons de récupérer plus de logs
    if not billing_logs and not debug_logs:
        print(f"\n⚠️ Aucun log de billing/debug trouvé dans les {len(logs)} derniers logs.")
        print("Essayons de récupérer plus de logs...")
        
        # Récupérer des logs plus anciens
        if logs_data.get('hasMore'):
            next_start = logs_data.get('nextStartTime')
            next_end = logs_data.get('nextEndTime')
            
            if next_start and next_end:
                print(f"Récupération de logs plus anciens...")
                older_logs_data = get_logs(limit=50, start_time=next_start, end_time=next_end)
                
                if older_logs_data and 'logs' in older_logs_data:
                    older_logs = older_logs_data['logs']
                    print(f"✅ {len(older_logs)} logs plus anciens récupérés")
                    
                    # Analyser les logs plus anciens
                    for log in older_logs:
                        message = log.get('message', '').lower()
                        if any(keyword in message for keyword in ['billing', 'subscription', 'tier', 'stripe', 'debug', 'can_use_model']):
                            timestamp = log.get('timestamp', 'N/A')
                            original_message = log.get('message', 'N/A')
                            print(f"[{timestamp}] {original_message}")
    
    # Sauvegarder tous les logs pour analyse
    with open('all_logs_analysis.json', 'w') as f:
        json.dump({
            'logs': logs,
            'error_403_logs': error_403_logs,
            'billing_logs': billing_logs,
            'debug_logs': debug_logs,
            'agent_initiate_logs': agent_initiate_logs
        }, f, indent=2)
    
    print(f"\n📄 Logs sauvegardés dans: all_logs_analysis.json")
    
    return {
        'error_403_count': len(error_403_logs),
        'billing_logs_count': len(billing_logs),
        'debug_logs_count': len(debug_logs),
        'has_403_errors': len(error_403_logs) > 0
    }

def main():
    """Fonction principale"""
    print("🔧 ANALYSE DES LOGS RENDER - DIAGNOSTIC PROBLÈME 403")
    print("=" * 60)
    
    results = analyze_logs()
    
    print(f"\n📋 CONCLUSION:")
    if results['has_403_errors']:
        print("❌ Problème confirmé: Erreurs 403 détectées lors de l'initiation d'agent")
        print("💡 Prochaines étapes:")
        print("   1. Tester directement les endpoints de billing")
        print("   2. Vérifier la configuration Stripe")
        print("   3. Examiner le code de can_use_model")
    else:
        print("✅ Aucune erreur 403 récente détectée")
    
    if results['billing_logs_count'] == 0 and results['debug_logs_count'] == 0:
        print("⚠️ Aucun log de billing/debug trouvé - les logs de debug ne sont peut-être pas activés")

if __name__ == "__main__":
    main()
