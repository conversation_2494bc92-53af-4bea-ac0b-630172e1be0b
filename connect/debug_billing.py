#!/usr/bin/env python3
"""
Script de diagnostic pour le problème de billing avec codes promo Stripe
"""

import requests
import json
import sys
import os
from typing import Dict, Any

# Configuration
BACKEND_URL = "https://connect-backend.onrender.com"  # Ajustez selon votre URL Render
# BACKEND_URL = "http://localhost:8000"  # Pour test local

def test_billing_endpoints(auth_token: str) -> Dict[str, Any]:
    """
    Teste les endpoints de billing pour diagnostiquer le problème
    """
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json"
    }
    
    results = {}
    
    # Test 1: Vérifier l'authentification
    print("🔍 Test 1: Vérification de l'authentification...")
    try:
        response = requests.get(f"{BACKEND_URL}/api/auth/me", headers=headers)
        if response.status_code == 200:
            user_data = response.json()
            print(f"✅ Authentifié en tant que: {user_data.get('user', {}).get('email', 'N/A')}")
            results['auth'] = user_data
        else:
            print(f"❌ Erreur d'authentification: {response.status_code}")
            print(f"Response: {response.text}")
            return results
    except Exception as e:
        print(f"❌ Erreur lors du test d'authentification: {e}")
        return results
    
    # Test 2: Vérifier le statut de billing
    print("\n🔍 Test 2: Vérification du statut de billing...")
    try:
        response = requests.get(f"{BACKEND_URL}/api/billing/check-status", headers=headers)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            billing_status = response.json()
            print(f"✅ Statut de billing récupéré:")
            print(json.dumps(billing_status, indent=2))
            results['billing_status'] = billing_status
        else:
            print(f"❌ Erreur lors de la vérification du billing: {response.status_code}")
            print(f"Response: {response.text}")
            results['billing_error'] = {
                'status_code': response.status_code,
                'response': response.text
            }
    except Exception as e:
        print(f"❌ Erreur lors du test de billing: {e}")
        results['billing_exception'] = str(e)
    
    # Test 3: Vérifier la subscription
    print("\n🔍 Test 3: Vérification de la subscription...")
    try:
        response = requests.get(f"{BACKEND_URL}/api/billing/subscription", headers=headers)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            subscription_data = response.json()
            print(f"✅ Données de subscription récupérées:")
            print(json.dumps(subscription_data, indent=2))
            results['subscription'] = subscription_data
        else:
            print(f"❌ Erreur lors de la récupération de la subscription: {response.status_code}")
            print(f"Response: {response.text}")
            results['subscription_error'] = {
                'status_code': response.status_code,
                'response': response.text
            }
    except Exception as e:
        print(f"❌ Erreur lors du test de subscription: {e}")
        results['subscription_exception'] = str(e)
    
    # Test 4: Tester l'initiation d'agent (pour voir l'erreur 403)
    print("\n🔍 Test 4: Test d'initiation d'agent...")
    try:
        # Données minimales pour tester l'initiation
        form_data = {
            'prompt': 'Test de diagnostic',
            'model_name': 'openrouter/deepseek/deepseek-chat',  # Modèle gratuit
            'stream': 'false'
        }
        
        response = requests.post(
            f"{BACKEND_URL}/api/agent/initiate",
            headers={"Authorization": f"Bearer {auth_token}"},
            data=form_data
        )
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 403:
            print(f"❌ Erreur 403 confirmée:")
            print(f"Response: {response.text}")
            results['agent_initiate_403'] = {
                'status_code': response.status_code,
                'response': response.text
            }
        elif response.status_code == 200:
            print(f"✅ Agent initié avec succès (problème résolu?)")
            agent_data = response.json()
            results['agent_initiate_success'] = agent_data
        else:
            print(f"⚠️ Autre erreur: {response.status_code}")
            print(f"Response: {response.text}")
            results['agent_initiate_other'] = {
                'status_code': response.status_code,
                'response': response.text
            }
    except Exception as e:
        print(f"❌ Erreur lors du test d'initiation d'agent: {e}")
        results['agent_initiate_exception'] = str(e)
    
    return results

def main():
    """
    Fonction principale
    """
    print("🔧 DIAGNOSTIC DU PROBLÈME DE BILLING AVEC CODES PROMO STRIPE")
    print("=" * 60)
    
    # Demander le token d'authentification
    auth_token = input("\n🔑 Veuillez entrer votre token d'authentification JWT: ").strip()
    
    if not auth_token:
        print("❌ Token d'authentification requis")
        sys.exit(1)
    
    # Exécuter les tests
    results = test_billing_endpoints(auth_token)
    
    # Sauvegarder les résultats
    with open('billing_diagnostic_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Résultats sauvegardés dans: billing_diagnostic_results.json")
    
    # Analyse des résultats
    print("\n📊 ANALYSE DES RÉSULTATS:")
    print("=" * 30)
    
    if 'billing_status' in results:
        billing = results['billing_status']
        print(f"Can run: {billing.get('can_run', 'N/A')}")
        print(f"Message: {billing.get('message', 'N/A')}")
        
        if 'subscription' in billing:
            sub = billing['subscription']
            if sub and 'items' in sub and sub['items'].get('data'):
                price_id = sub['items']['data'][0]['price']['id']
                print(f"Price ID détecté: {price_id}")
            else:
                print("⚠️ Structure de subscription inattendue")
    
    if 'agent_initiate_403' in results:
        print("\n❌ PROBLÈME CONFIRMÉ: Erreur 403 lors de l'initiation d'agent")
        try:
            error_detail = json.loads(results['agent_initiate_403']['response'])
            if 'detail' in error_detail and 'message' in error_detail['detail']:
                print(f"Message d'erreur: {error_detail['detail']['message']}")
        except:
            print(f"Réponse brute: {results['agent_initiate_403']['response']}")

if __name__ == "__main__":
    main()
