# Redis Configuration for CONNECT
timeout 120

# Network
bind 0.0.0.0
protected-mode no
port 6379

# Memory management
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Logging
loglevel notice

# Authentication (uncomment and set password if needed)
# requirepass votre_mot_de_passe_redis_securise

# No authentication required for internal service (current setup)
# requirepass is commented out - no password needed
