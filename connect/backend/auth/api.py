"""
Authentication API module for Orchestra Connect

This module provides authentication endpoints and user management functionality.
"""

from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel
from typing import Optional, Dict, Any
from utils.auth_utils import get_current_user_id_from_jwt, get_optional_user_id
from services.supabase import DBConnection
from utils.logger import logger
import json

router = APIRouter(tags=["auth"])

# Pydantic models
class UserProfile(BaseModel):
    id: str
    email: str
    created_at: str
    updated_at: Optional[str] = None
    user_metadata: Optional[Dict[str, Any]] = None
    app_metadata: Optional[Dict[str, Any]] = None

class AuthResponse(BaseModel):
    user: UserProfile
    authenticated: bool = True

class AuthStatus(BaseModel):
    authenticated: bool
    user_id: Optional[str] = None

# Initialize database connection
db = DBConnection()

@router.get("/auth/me", response_model=AuthResponse)
async def get_current_user(
    user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Get current authenticated user information."""
    try:
        logger.info(f"Getting user info for user_id: {user_id}")
        
        # Get Supabase client
        client = await db.client
        
        # Get user from Supabase Auth
        user_result = await client.auth.admin.get_user_by_id(user_id)
        
        if not user_result or not user_result.user:
            logger.error(f"User not found in Supabase: {user_id}")
            raise HTTPException(status_code=404, detail="User not found")
        
        user = user_result.user
        
        # Create user profile response
        user_profile = UserProfile(
            id=user.id,
            email=user.email or "",
            created_at=user.created_at,
            updated_at=user.updated_at,
            user_metadata=user.user_metadata or {},
            app_metadata=user.app_metadata or {}
        )
        
        logger.info(f"Successfully retrieved user info for: {user.email}")
        
        return AuthResponse(user=user_profile)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting current user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get user information: {str(e)}")

@router.get("/user/me", response_model=AuthResponse)
async def get_user_profile(
    user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Get user profile (alias for /auth/me for compatibility)."""
    return await get_current_user(user_id)

@router.get("/auth/status", response_model=AuthStatus)
async def get_auth_status(
    user_id: Optional[str] = Depends(get_optional_user_id)
):
    """Check authentication status without requiring authentication."""
    try:
        if user_id:
            logger.info(f"User is authenticated: {user_id}")
            return AuthStatus(authenticated=True, user_id=user_id)
        else:
            logger.info("User is not authenticated")
            return AuthStatus(authenticated=False)
    except Exception as e:
        logger.error(f"Error checking auth status: {str(e)}")
        return AuthStatus(authenticated=False)

@router.post("/auth/refresh")
async def refresh_token(
    request: Request,
    user_id: str = Depends(get_current_user_id_from_jwt)
):
    """Refresh authentication token."""
    try:
        logger.info(f"Refreshing token for user: {user_id}")
        
        # Get authorization header
        auth_header = request.headers.get("authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Invalid authorization header")
        
        token = auth_header.split(" ")[1]
        
        # Get Supabase client
        client = await db.client
        
        # Verify the token is still valid
        user_result = await client.auth.admin.get_user_by_id(user_id)
        
        if not user_result or not user_result.user:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        return {
            "message": "Token is valid",
            "user_id": user_id,
            "expires_at": None  # Supabase handles token expiration
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error refreshing token: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to refresh token")

@router.get("/auth/health")
async def auth_health_check():
    """Health check for authentication service."""
    try:
        # Test database connection
        client = await db.client
        
        return {
            "status": "ok",
            "service": "authentication",
            "database": "connected"
        }
    except Exception as e:
        logger.error(f"Auth health check failed: {str(e)}")
        return {
            "status": "error",
            "service": "authentication",
            "database": "disconnected",
            "error": str(e)
        }
