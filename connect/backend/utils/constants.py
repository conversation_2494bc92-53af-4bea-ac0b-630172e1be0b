MODEL_ACCESS_TIERS = {
    "free": [
        "openrouter/deepseek/deepseek-chat",
        "openrouter/qwen/qwen3-235b-a22b",
        "openrouter/google/gemini-2.5-flash-preview-05-20",
    ],
    "student": [
        # Mod<PERSON>les gratuits (hérités du tier free)
        "openrouter/deepseek/deepseek-chat",
        "openrouter/qwen/qwen3-235b-a22b",
        "openrouter/google/gemini-2.5-flash-preview-05-20",
        "openrouter/google/gemini-2.5-flash-preview-05-20:thinking",
        # Modèles payants pour tier student
        "openai/gpt-4o",
        "gpt-image-1",
        "anthropic/claude-3-7-sonnet-latest",
        "anthropic/claude-sonnet-4-20250514",
        "openrouter/google/gemini-2.5-pro-preview",
    ],
    "essential": [
        # Modèles gratuits
        "openrouter/deepseek/deepseek-chat",
        "openrouter/qwen/qwen3-235b-a22b",
        "openrouter/google/gemini-2.5-flash-preview-05-20",
        "openrouter/google/gemini-2.5-flash-preview-05-20:thinking",
        # Modèles payants
        "openai/gpt-4o",
        "gpt-image-1",
        "anthropic/claude-3-7-sonnet-latest",
        "anthropic/claude-sonnet-4-20250514",
        "openrouter/google/gemini-2.5-pro-preview",
    ],
    "pro": [
        # Modèles gratuits
        "openrouter/deepseek/deepseek-chat",
        "openrouter/qwen/qwen3-235b-a22b",
        "openrouter/google/gemini-2.5-flash-preview-05-20",
        "openrouter/google/gemini-2.5-flash-preview-05-20:thinking",
        # Modèles payants
        "openai/gpt-4o",
        "gpt-image-1",
        "anthropic/claude-3-7-sonnet-latest",
        "anthropic/claude-sonnet-4-20250514",
        "openrouter/google/gemini-2.5-pro-preview",
    ],
    "tier_25_200": [
        # Modèles gratuits
        "openrouter/deepseek/deepseek-chat",
        "openrouter/qwen/qwen3-235b-a22b",
        "openrouter/google/gemini-2.5-flash-preview-05-20",
        "openrouter/google/gemini-2.5-flash-preview-05-20:thinking",
        # Modèles payants
        "openai/gpt-4o",
        "gpt-image-1",
        "anthropic/claude-3-7-sonnet-latest",
        "anthropic/claude-sonnet-4-20250514",
        "openrouter/google/gemini-2.5-pro-preview",
    ],
    "enterprise": [
        # Modèles gratuits
        "openrouter/deepseek/deepseek-chat",
        "openrouter/qwen/qwen3-235b-a22b",
        "openrouter/google/gemini-2.5-flash-preview-05-20",
        "openrouter/google/gemini-2.5-flash-preview-05-20:thinking",
        # Modèles payants
        "openai/gpt-4o",
        "gpt-image-1",
        "anthropic/claude-3-7-sonnet-latest",
        "anthropic/claude-sonnet-4-20250514",
        "openrouter/google/gemini-2.5-pro-preview",
    ],
    "tier_125_800": [
        # Modèles gratuits
        "openrouter/deepseek/deepseek-chat",
        "openrouter/qwen/qwen3-235b-a22b",
        "openrouter/google/gemini-2.5-flash-preview-05-20",
        "openrouter/google/gemini-2.5-flash-preview-05-20:thinking",
        # Modèles payants
        "openai/gpt-4o",
        "gpt-image-1",
        "anthropic/claude-3-7-sonnet-latest",
        "anthropic/claude-sonnet-4-20250514",
        "openrouter/google/gemini-2.5-pro-preview",
    ],
    "tier_200_1000": [
        # Modèles gratuits
        "openrouter/deepseek/deepseek-chat",
        "openrouter/qwen/qwen3-235b-a22b",
        "openrouter/google/gemini-2.5-flash-preview-05-20",
        "openrouter/google/gemini-2.5-flash-preview-05-20:thinking",
        # Modèles payants
        "openai/gpt-4o",
        "gpt-image-1",
        "anthropic/claude-3-7-sonnet-latest",
        "anthropic/claude-sonnet-4-20250514",
        "openrouter/google/gemini-2.5-pro-preview",
    ],
}
MODEL_NAME_ALIASES = {
    # Business-friendly agent names to backend models
    "super-agent": "openrouter/google/gemini-2.5-flash-preview-05-20",
    "agent-strategique": "openrouter/google/gemini-2.5-pro-preview",
    "agent-design": "gpt-image-1",

    # Legacy short names to full names (for compatibility)
    "sonnet-3.7": "anthropic/claude-3-7-sonnet-latest",
    "sonnet-3.5": "anthropic/claude-3-5-sonnet-latest",
    "haiku-3.5": "anthropic/claude-3-5-haiku-latest",
    "claude-sonnet-4": "anthropic/claude-sonnet-4-20250514",
    "gpt-4o": "openai/gpt-4o",
    "gpt-4.1": "openai/gpt-4.1",
    "gpt-4.1-mini": "gpt-4.1-mini",
    # "gpt-4-turbo": "openai/gpt-4-turbo",  # Commented out in constants.py
    # "gpt-4": "openai/gpt-4",  # Commented out in constants.py
    # "gemini-flash-2.5": "openrouter/google/gemini-2.5-flash-preview",  # Commented out in constants.py
    # "grok-3": "xai/grok-3-fast-latest",  # Commented out in constants.py
    # "deepseek-r1": "openrouter/deepseek/deepseek-r1",
    # "grok-3-mini": "xai/grok-3-mini-fast-beta",  # Commented out in constants.py
    "gemini-flash-2.5": "openrouter/google/gemini-2.5-flash-preview-05-20",
    "gemini-2.5-flash:thinking":"openrouter/google/gemini-2.5-flash-preview-05-20:thinking",
    
    # "google/gemini-2.5-flash-preview":"openrouter/google/gemini-2.5-flash-preview",
    # "google/gemini-2.5-flash-preview:thinking":"openrouter/google/gemini-2.5-flash-preview:thinking",
    "google/gemini-2.5-pro-preview":"openrouter/google/gemini-2.5-pro-preview",


    # Also include full names as keys to ensure they map to themselves
    # "anthropic/claude-3-7-sonnet-latest": "anthropic/claude-3-7-sonnet-latest",
    # "openai/gpt-4.1-2025-04-14": "openai/gpt-4.1-2025-04-14",  # Commented out in constants.py
    # "openai/gpt-4o": "openai/gpt-4o",
    # "openai/gpt-4-turbo": "openai/gpt-4-turbo",  # Commented out in constants.py
    # "openai/gpt-4": "openai/gpt-4",  # Commented out in constants.py
    # "openrouter/google/gemini-2.5-flash-preview": "openrouter/google/gemini-2.5-flash-preview",  # Commented out in constants.py
    # "xai/grok-3-fast-latest": "xai/grok-3-fast-latest",  # Commented out in constants.py
    # "deepseek/deepseek-chat": "openrouter/deepseek/deepseek-chat",    
    # "deepseek/deepseek-r1": "openrouter/deepseek/deepseek-r1",
    
    # "qwen/qwen3-235b-a22b": "openrouter/qwen/qwen3-235b-a22b",
    # "xai/grok-3-mini-fast-beta": "xai/grok-3-mini-fast-beta",  # Commented out in constants.py
}