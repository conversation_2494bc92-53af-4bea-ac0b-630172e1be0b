#!/usr/bin/env python3
"""
Diagnostic complet pour identifier le problème 403
"""

import requests
import json
import sys

# Configuration
BACKEND_URL = "https://connect-backend-p2ex.onrender.com"

def test_endpoints():
    """Test complet des endpoints"""
    print("🔧 DIAGNOSTIC COMPLET DU PROBLÈME 403")
    print("=" * 60)
    
    # Demander le token d'authentification
    auth_token = input("\n🔑 Entrez votre token JWT: ").strip()
    
    if not auth_token:
        print("❌ Token requis")
        return
    
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json"
    }
    
    results = {}
    
    # Test 1: Auth
    print("\n🔍 Test 1: Authentification...")
    try:
        response = requests.get(f"{BACKEND_URL}/api/auth/me", headers=headers)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            user_data = response.json()
            print(f"✅ User: {user_data.get('user', {}).get('email', 'N/A')}")
            results['auth'] = user_data
        else:
            print(f"❌ Auth failed: {response.text}")
            return
    except Exception as e:
        print(f"❌ Auth error: {e}")
        return
    
    # Test 2: Billing Status
    print("\n🔍 Test 2: Billing Status...")
    try:
        response = requests.get(f"{BACKEND_URL}/api/billing/check-status", headers=headers)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            billing = response.json()
            print(f"✅ Can run: {billing.get('can_run')}")
            print(f"Message: {billing.get('message')}")
            if billing.get('subscription'):
                sub = billing['subscription']
                print(f"Subscription status: {sub.get('status', 'N/A')}")
                if sub.get('items', {}).get('data'):
                    price_id = sub['items']['data'][0]['price']['id']
                    print(f"Price ID: {price_id}")
            results['billing'] = billing
        else:
            print(f"❌ Billing error: {response.text}")
            results['billing_error'] = response.text
    except Exception as e:
        print(f"❌ Billing exception: {e}")
    
    # Test 3: Subscription
    print("\n🔍 Test 3: Subscription...")
    try:
        response = requests.get(f"{BACKEND_URL}/api/billing/subscription", headers=headers)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            sub_data = response.json()
            print(f"✅ Subscription data retrieved")
            print(json.dumps(sub_data, indent=2))
            results['subscription'] = sub_data
        else:
            print(f"❌ Subscription error: {response.text}")
    except Exception as e:
        print(f"❌ Subscription exception: {e}")
    
    # Test 4: Test modèles spécifiques
    print("\n🔍 Test 4: Test des modèles...")
    
    models_to_test = [
        "openrouter/deepseek/deepseek-chat",  # Gratuit
        "openrouter/google/gemini-2.5-flash-preview-05-20",  # Gratuit
        "openai/gpt-4o",  # Payant
        "anthropic/claude-3-7-sonnet-latest",  # Payant
        "claude-sonnet-4"  # Alias
    ]
    
    for model in models_to_test:
        print(f"\n   🧪 Test modèle: {model}")
        try:
            form_data = {
                'prompt': 'Test diagnostic',
                'model_name': model,
                'stream': 'false'
            }
            
            response = requests.post(
                f"{BACKEND_URL}/api/agent/initiate",
                headers={"Authorization": f"Bearer {auth_token}"},
                data=form_data
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 403:
                print(f"   ❌ 403 pour {model}")
                try:
                    error_detail = response.json()
                    print(f"   Détail: {error_detail}")
                    
                    # Analyser le message d'erreur
                    if 'detail' in error_detail:
                        detail = error_detail['detail']
                        if isinstance(detail, dict) and 'message' in detail:
                            message = detail['message']
                            print(f"   Message: {message}")
                            
                            # Vérifier si c'est un problème de modèle ou de billing
                            if 'subscription plan does not include access' in message:
                                print(f"   🎯 PROBLÈME: Accès au modèle refusé")
                                if 'allowed_models' in detail:
                                    allowed = detail['allowed_models']
                                    print(f"   Modèles autorisés: {allowed}")
                            elif 'limit' in message.lower():
                                print(f"   🎯 PROBLÈME: Limite de crédit atteinte")
                        
                except Exception as parse_error:
                    print(f"   Erreur parsing: {parse_error}")
                    print(f"   Response brute: {response.text}")
                
                results[f'model_403_{model}'] = {
                    'status_code': response.status_code,
                    'response': response.text
                }
                
            elif response.status_code == 200:
                print(f"   ✅ Succès pour {model}")
                results[f'model_success_{model}'] = True
                
            else:
                print(f"   ⚠️ Autre erreur {response.status_code}: {response.text}")
                results[f'model_other_{model}'] = {
                    'status_code': response.status_code,
                    'response': response.text
                }
                
        except Exception as e:
            print(f"   ❌ Exception pour {model}: {e}")
    
    # Test 5: Vérifier les variables d'environnement via un endpoint de debug
    print("\n🔍 Test 5: Variables d'environnement...")
    try:
        response = requests.get(f"{BACKEND_URL}/api/health", headers=headers)
        print(f"Health check status: {response.status_code}")
    except Exception as e:
        print(f"Health check error: {e}")
    
    # Sauvegarder les résultats
    with open('diagnostic_complet_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Résultats sauvegardés dans: diagnostic_complet_results.json")
    
    # Analyse finale
    print("\n📊 ANALYSE FINALE:")
    print("=" * 30)
    
    # Compter les erreurs par type
    model_403_count = len([k for k in results.keys() if 'model_403_' in k])
    model_success_count = len([k for k in results.keys() if 'model_success_' in k])
    
    if model_403_count > 0:
        print(f"❌ {model_403_count} modèles bloqués par erreur 403")
        
        # Analyser les messages d'erreur
        for key, value in results.items():
            if 'model_403_' in key:
                model_name = key.replace('model_403_', '')
                print(f"   - {model_name}: Bloqué")
    
    if model_success_count > 0:
        print(f"✅ {model_success_count} modèles fonctionnent")
    
    # Recommandations
    print(f"\n💡 RECOMMANDATIONS:")
    if model_403_count > 0:
        print("1. Vérifier la configuration des tiers de modèles")
        print("2. Vérifier les clés API (OpenRouter, OpenAI, Anthropic)")
        print("3. Vérifier la détection du tier de subscription")
    
    return results

if __name__ == "__main__":
    test_endpoints()
