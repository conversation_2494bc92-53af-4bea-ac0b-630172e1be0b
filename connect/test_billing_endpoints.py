#!/usr/bin/env python3
"""
Script pour tester directement les endpoints de billing et diagnostiquer le problème 403
"""

import requests
import json
import sys

# Configuration
BACKEND_URL = "https://connect-backend-p2ex.onrender.com"

def test_billing_endpoints():
    """
    Teste les endpoints de billing pour diagnostiquer le problème
    """
    print("🔧 TEST DES ENDPOINTS DE BILLING")
    print("=" * 50)
    
    # Demander le token d'authentification
    auth_token = input("\n🔑 Veuillez entrer votre token d'authentification JWT: ").strip()
    
    if not auth_token:
        print("❌ Token d'authentification requis")
        return
    
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json"
    }
    
    results = {}
    
    # Test 1: Vérifier l'authentification
    print("\n🔍 Test 1: Vérification de l'authentification...")
    try:
        response = requests.get(f"{BACKEND_URL}/api/auth/me", headers=headers)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            user_data = response.json()
            print(f"✅ Authentifié en tant que: {user_data.get('user', {}).get('email', 'N/A')}")
            results['auth'] = user_data
        else:
            print(f"❌ Erreur d'authentification: {response.status_code}")
            print(f"Response: {response.text}")
            return results
    except Exception as e:
        print(f"❌ Erreur lors du test d'authentification: {e}")
        return results
    
    # Test 2: Vérifier le statut de billing
    print("\n🔍 Test 2: Vérification du statut de billing...")
    try:
        response = requests.get(f"{BACKEND_URL}/api/billing/check-status", headers=headers)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            billing_status = response.json()
            print(f"✅ Statut de billing récupéré:")
            print(json.dumps(billing_status, indent=2))
            results['billing_status'] = billing_status
        else:
            print(f"❌ Erreur lors de la vérification du billing: {response.status_code}")
            print(f"Response: {response.text}")
            results['billing_error'] = {
                'status_code': response.status_code,
                'response': response.text
            }
    except Exception as e:
        print(f"❌ Erreur lors du test de billing: {e}")
        results['billing_exception'] = str(e)
    
    # Test 3: Vérifier la subscription
    print("\n🔍 Test 3: Vérification de la subscription...")
    try:
        response = requests.get(f"{BACKEND_URL}/api/billing/subscription", headers=headers)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            subscription_data = response.json()
            print(f"✅ Données de subscription récupérées:")
            print(json.dumps(subscription_data, indent=2))
            results['subscription'] = subscription_data
        else:
            print(f"❌ Erreur lors de la récupération de la subscription: {response.status_code}")
            print(f"Response: {response.text}")
            results['subscription_error'] = {
                'status_code': response.status_code,
                'response': response.text
            }
    except Exception as e:
        print(f"❌ Erreur lors du test de subscription: {e}")
        results['subscription_exception'] = str(e)
    
    # Test 4: Tester l'initiation d'agent avec différents modèles
    print("\n🔍 Test 4: Test d'initiation d'agent avec différents modèles...")
    
    models_to_test = [
        "openrouter/deepseek/deepseek-chat",  # Modèle gratuit
        "openai/gpt-4o",  # Modèle payant
        "anthropic/claude-3-7-sonnet-latest"  # Modèle premium
    ]
    
    for model in models_to_test:
        print(f"\n   🧪 Test avec le modèle: {model}")
        try:
            form_data = {
                'prompt': 'Test de diagnostic',
                'model_name': model,
                'stream': 'false'
            }
            
            response = requests.post(
                f"{BACKEND_URL}/api/agent/initiate",
                headers={"Authorization": f"Bearer {auth_token}"},
                data=form_data
            )
            
            print(f"   Status Code: {response.status_code}")
            if response.status_code == 403:
                print(f"   ❌ Erreur 403 confirmée pour {model}")
                try:
                    error_detail = response.json()
                    print(f"   Détail: {json.dumps(error_detail, indent=4)}")
                except:
                    print(f"   Response brute: {response.text}")
                
                results[f'agent_403_{model}'] = {
                    'status_code': response.status_code,
                    'response': response.text
                }
            elif response.status_code == 200:
                print(f"   ✅ Agent initié avec succès pour {model}")
                results[f'agent_success_{model}'] = True
            else:
                print(f"   ⚠️ Autre erreur pour {model}: {response.status_code}")
                print(f"   Response: {response.text}")
                results[f'agent_other_{model}'] = {
                    'status_code': response.status_code,
                    'response': response.text
                }
        except Exception as e:
            print(f"   ❌ Erreur lors du test avec {model}: {e}")
            results[f'agent_exception_{model}'] = str(e)
    
    # Sauvegarder les résultats
    with open('billing_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Résultats sauvegardés dans: billing_test_results.json")
    
    # Analyse des résultats
    print("\n📊 ANALYSE DES RÉSULTATS:")
    print("=" * 30)
    
    if 'billing_status' in results:
        billing = results['billing_status']
        print(f"Can run: {billing.get('can_run', 'N/A')}")
        print(f"Message: {billing.get('message', 'N/A')}")
        
        if 'subscription' in billing:
            sub = billing['subscription']
            if sub and isinstance(sub, dict):
                if 'items' in sub and sub['items'].get('data'):
                    price_id = sub['items']['data'][0]['price']['id']
                    print(f"Price ID détecté: {price_id}")
                else:
                    print("⚠️ Structure de subscription inattendue")
                    print(f"Subscription data: {sub}")
    
    # Compter les erreurs 403
    error_403_count = len([k for k in results.keys() if 'agent_403_' in k])
    success_count = len([k for k in results.keys() if 'agent_success_' in k])
    
    if error_403_count > 0:
        print(f"\n❌ PROBLÈME CONFIRMÉ: {error_403_count} erreurs 403 lors de l'initiation d'agent")
        print("💡 Le problème semble être lié à la vérification des modèles autorisés")
    elif success_count > 0:
        print(f"\n✅ {success_count} modèles fonctionnent correctement")
    
    return results

if __name__ == "__main__":
    test_billing_endpoints()
