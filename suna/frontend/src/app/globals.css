@import 'tailwindcss';
@import 'tw-animate-css';
@plugin 'tailwind-scrollbar';
@plugin 'tailwind-scrollbar-hide';

@custom-variant dark (&:where(.dark, .dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-marquee: marquee var(--duration) infinite linear;
  --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;
  --animate-orbit: orbit calc(var(--duration) * 1s) linear infinite;
  --animate-slide-down: slide-down 300ms cubic-bezier(0.87, 0, 0.13, 1);
  --animate-slide-up: slide-up 300ms cubic-bezier(0.87, 0, 0.13, 1);
  --scaleIn: scaleIn 200ms ease;
  --scaleOut: scaleOut 200ms ease;
  --fadeIn: fadeIn 200ms ease;
  --fadeOut: fadeOut 200ms ease;
  --enterFromLeft: enterFromLeft 250ms ease;
  --enterFromRight: enterFromRight 250ms ease;
  --exitToLeft: exitToLeft 250ms ease;
  --exitToRight: exitToRight 250ms ease;
  --animate-elliptical-orbit: elliptical-orbit 20s linear infinite;
  @keyframes orbit {
    0% {
      transform: rotate(calc(var(--angle) * 1deg))
        translateY(calc(var(--radius) * 1px)) rotate(calc(var(--angle) * -1deg));
    }
    100% {
      transform: rotate(calc(var(--angle) * 1deg + 360deg))
        translateY(calc(var(--radius) * 1px))
        rotate(calc((var(--angle) * -1deg) - 360deg));
    }
  }

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }

  @keyframes marquee {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }

  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }
  @keyframes slide-down {
    from {
      height: 0px;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes slide-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0px;
    }
  }

  /* Add this to your globals.css */
  @keyframes enterFromRight {
    from {
      opacity: 0;
      transform: translateX(200px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes enterFromLeft {
    from {
      opacity: 0;
      transform: translateX(-200px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes exitToRight {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(200px);
    }
  }

  @keyframes exitToLeft {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(-200px);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: rotateX(-30deg) scale(0.9);
    }
    to {
      opacity: 1;
      transform: rotateX(0deg) scale(1);
    }
  }

  @keyframes scaleOut {
    from {
      opacity: 1;
      transform: rotateX(0deg) scale(1);
    }
    to {
      opacity: 0;
      transform: rotateX(-10deg) scale(0.95);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fadeOut {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }

  @keyframes elliptical-orbit {
    from {
      transform: rotate(var(--angle, 0) deg)
        translate(var(--h-radius, 160px), 0)
        rotate(calc(var(--angle, 0) deg * -1));
    }
    to {
      transform: rotate(calc(var(--angle, 0) deg + 360deg))
        translate(var(--h-radius, 160px), 0)
        rotate(calc((var(--angle, 0) deg + 360deg) * -1));
    }
  }
}

:root {
  --background: oklch(98.46% 0.002 247.84);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(54.65% 0.246 262.87);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.145 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(98.46% 0.002 247.84);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.185 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(54.65% 0.246 262.87);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(27.39% 0.005 286.03);
  --accent-foreground: oklch(98.46% 0.002 247.84)
    --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(21.03% 0.006 285.89);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  html {
    scroll-behavior: smooth;
    zoom: 100%;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: 'palt'; /* Adjusts spacing for CJK text */
  }

  /* Add font fallbacks for CJK characters */
  .cjk-text,
  .prose p,
  .prose li,
  .prose table td,
  .prose table th,
  .markdown-content {
    font-family:
      var(--font-sans),
      ui-sans-serif,
      -apple-system,
      'Segoe UI',
      'Helvetica Neue',
      'Noto Sans',
      'Noto Sans CJK JP',
      'Noto Sans CJK KR',
      'Noto Sans CJK SC',
      'Noto Sans CJK TC',
      sans-serif;
    line-height: 1.7;
  }

  /* Specific handling for monospace/code with CJK character support */
  code,
  pre,
  .font-mono {
    font-family:
      var(--font-mono), ui-monospace, SFMono-Regular, 'SF Mono', Menlo, Monaco,
      Consolas, 'Liberation Mono', 'Courier New', monospace,
      'Noto Sans Mono CJK JP', 'Noto Sans Mono CJK KR', 'Noto Sans Mono CJK SC',
      'Noto Sans Mono CJK TC';
  }
}

/* Custom styles for Markdown chat content */
.prose.chat-markdown {
  /* Ensure reasonable spacing */
  & > * + * {
    margin-top: 0.75em;
  }

  /* Fix headings to be more compact in chat context */
  & h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-top: 1em;
    margin-bottom: 0.5em;
    line-height: 1.2;
  }

  & h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 0.8em;
    margin-bottom: 0.4em;
    line-height: 1.3;
  }

  & h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-top: 0.6em;
    margin-bottom: 0.3em;
  }

  & h4,
  & h5,
  & h6 {
    font-size: 1rem;
    font-weight: 600;
    margin-top: 0.5em;
    margin-bottom: 0.25em;
  }

  /* Improve lists */
  & ul,
  & ol {
    padding-left: 1.5em;
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  & ul {
    list-style-type: disc;
  }

  & ol {
    list-style-type: decimal;
  }

  & li {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
  }

  & li > ul,
  & li > ol {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
  }

  /* Fix code blocks */
  & pre {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
    padding: 0.75em 1em;
    /* background 95 */
    background-color: theme('colors.background/95');
    border-radius: 0.375rem;
    overflow-x: auto;
    font-family: var(--font-mono);
  }

  & pre code {
    background-color: transparent;
    padding: 0;
    font-size: 0.9em;
    color: inherit;
    font-family: var(--font-mono);
    white-space: pre;
    word-break: normal;
    overflow-wrap: normal;
  }

  /* Fix inline code to ensure it wraps when needed */
  & code:not([class*='language-']) {
    padding: 0.2em 0.4em;
    font-size: 0.85em;
    font-family: var(--font-mono);
    background-color: theme('colors.background/95');
    border-radius: 3px;
    word-break: break-word;
  }

  /* Fix tables */
  & table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 0.75em;
    margin-bottom: 0.75em;
    font-size: 0.9em;
  }

  & th {
    background-color: theme('colors.slate.100');
    font-weight: 600;
    text-align: left;
    padding: 0.5em 0.75em;
  }

  & td {
    padding: 0.5em 0.75em;
    border: 1px solid theme('colors.slate.200');
  }

  /* Fix blockquotes */
  & blockquote {
    border-left: 3px solid theme('colors.slate.300');
    padding-left: 1em;
    margin-left: 0;
    font-style: italic;
    color: theme('colors.slate.600');
  }

  /* Dark mode specific overrides */
  .dark & {
    /* Code blocks in dark mode */
    & pre {
      background-color: theme('colors.background/95');
      /* border: 1px solid theme('colors.zinc.700'); */
    }

    & code:not([class*='language-']) {
      background-color: theme('colors.background/95');
      color: theme('colors.zinc.200');
      /* border: 1px solid theme('colors.zinc.700'); */
    }

    /* Tables in dark mode */
    & th {
      background-color: theme('colors.zinc.800');
      border-color: theme('colors.zinc.700');
    }

    & td {
      border-color: theme('colors.zinc.700');
    }

    /* Blockquotes in dark mode */
    & blockquote {
      border-left-color: theme('colors.zinc.600');
      color: theme('colors.zinc.400');
    }

    /* Syntax highlighting in dark mode */
    & .hljs-keyword,
    & .hljs-selector-tag,
    & .hljs-built_in,
    & .hljs-name,
    & .hljs-tag {
      color: theme('colors.blue.400');
    }

    & .hljs-string,
    & .hljs-title,
    & .hljs-section,
    & .hljs-attribute,
    & .hljs-literal,
    & .hljs-template-tag,
    & .hljs-template-variable,
    & .hljs-type,
    & .hljs-addition {
      color: theme('colors.green.400');
    }

    & .hljs-comment,
    & .hljs-quote,
    & .hljs-deletion,
    & .hljs-meta {
      color: theme('colors.zinc.500');
    }

    & .hljs-keyword,
    & .hljs-selector-tag,
    & .hljs-literal,
    & .hljs-title,
    & .hljs-section,
    & .hljs-doctag,
    & .hljs-type,
    & .hljs-name,
    & .hljs-strong {
      font-weight: bold;
    }
  }
}

.bg-pattern-grid {
  background-image: 
    linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

.dark .bg-pattern-grid {
  background-image: 
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-checker-pattern {
  background-image: linear-gradient(45deg, #f3f4f6 25%, transparent 25%),
    linear-gradient(-45deg, #f3f4f6 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #f3f4f6 75%),
    linear-gradient(-45deg, transparent 75%, #f3f4f6 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  background-color: #ffffff;
}

.dark .bg-checker-pattern-dark {
  background-image: linear-gradient(45deg, rgba(39, 39, 42, 0.7) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(39, 39, 42, 0.7) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(39, 39, 42, 0.7) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(39, 39, 42, 0.7) 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  background-color: rgba(24, 24, 27, 0.7);
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(200%); }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@theme {
  --animate-shimmer: shimmer 1s infinite;
  --animate-gradient-shift: gradient-shift 2s ease infinite;
}

.animate-shimmer {
  @apply relative overflow-hidden rounded-md;
  @apply bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10;
  background-size: 200% 200%;
  animation: var(--animate-gradient-shift);
}

.animate-shimmer::before {
  content: '';
  @apply absolute top-0 left-0 w-full h-full;
  background: linear-gradient(
    140deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: var(--animate-shimmer);
  width: 100%;
  transform: translateX(-100%);
}

.animate-shimmer-gray {
  @apply relative overflow-hidden rounded-md;
  @apply bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200;
  background-size: 200% 200%;
  animation: var(--animate-gradient-shift);
}

.animate-shimmer-gray::before {
  content: '';
  @apply absolute top-0 left-0 w-full h-full;
  background: linear-gradient(
    140deg
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: var(--animate-shimmer);
  width: 100%;
  transform: translateX(-100%);
}

@theme inline {
  --animate-shiny-text: shiny-text 5s infinite;
 
  @keyframes shiny-text {
    0%,
    90%,
    100% {
      background-position: calc(-100% - var(--shiny-width)) 0;
    }
    30%,
    60% {
      background-position: calc(100% + var(--shiny-width)) 0;
    }
  }
}