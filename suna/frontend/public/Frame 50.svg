<svg width="4557" height="3253" viewBox="0 0 4557 3253" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_27_22)" filter="url(#filter0_d_27_22)">
<rect width="3245" height="4549" transform="matrix(0 -1 1 0 4 3245)" fill="white"/>
<circle cx="2278.5" cy="1622.5" r="3002.5" transform="rotate(-90 2278.5 1622.5)" fill="url(#paint0_radial_27_22)"/>
<g clip-path="url(#paint1_angular_27_22_clip_path)" data-figma-skip-parse="true"><g transform="matrix(3.0025 -0.19909 0.19909 3.0025 2278.5 1622.5)"><foreignObject x="-1061.74" y="-1061.74" width="2123.47" height="2123.47"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(252, 252, 252, 1) 0deg,rgba(255, 255, 255, 1) 0.248477deg,rgba(0, 0, 0, 1) 50.625deg,rgba(0, 0, 0, 1) 51.9653deg,rgba(255, 255, 255, 1) 88.125deg,rgba(0, 0, 0, 1) 142.5deg,rgba(255, 255, 255, 1) 196.875deg,rgba(0, 0, 0, 1) 256.875deg,rgba(255, 255, 255, 1) 300deg,rgba(0, 0, 0, 1) 335.202deg,rgba(0, 0, 0, 1) 335.342deg,rgba(252, 252, 252, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><circle cx="2278.5" cy="1622.5" r="3002.5" transform="rotate(-90 2278.5 1622.5)" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.00069021427771076560},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.1406250},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.14434811472892761},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.24479167163372040},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39583334326744080},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.5468750},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.71354168653488159},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.83333331346511841},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.93111741542816162},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.93150448799133301}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:6005.0,&#34;m01&#34;:398.18096923828125,&#34;m02&#34;:-923.09051513671875,&#34;m10&#34;:-398.18096923828125,&#34;m11&#34;:6005.0,&#34;m12&#34;:-1180.909667968750},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;DIFFERENCE&#34;,&#34;visible&#34;:true}" style="mix-blend-mode:difference"/>
<g clip-path="url(#paint2_angular_27_22_clip_path)" data-figma-skip-parse="true"><g transform="matrix(3.0025 -0.19909 0.19909 3.0025 2278.5 1622.5)"><foreignObject x="-1061.74" y="-1061.74" width="2123.47" height="2123.47"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(252, 252, 252, 1) 0deg,rgba(255, 255, 255, 1) 0.248477deg,rgba(0, 0, 0, 1) 50.625deg,rgba(0, 0, 0, 1) 51.9653deg,rgba(255, 255, 255, 1) 88.125deg,rgba(0, 0, 0, 1) 142.5deg,rgba(255, 255, 255, 1) 196.875deg,rgba(0, 0, 0, 1) 256.875deg,rgba(255, 255, 255, 1) 300deg,rgba(0, 0, 0, 1) 335.202deg,rgba(0, 0, 0, 1) 335.342deg,rgba(252, 252, 252, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><circle cx="2278.5" cy="1622.5" r="3002.5" transform="rotate(-90 2278.5 1622.5)" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.00069021427771076560},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.1406250},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.14434811472892761},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.24479167163372040},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39583334326744080},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.5468750},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.71354168653488159},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.83333331346511841},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.93111741542816162},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:0.0,&#34;b&#34;:0.0,&#34;a&#34;:1.0},&#34;position&#34;:0.93150448799133301}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:6005.0,&#34;m01&#34;:398.18096923828125,&#34;m02&#34;:-923.09051513671875,&#34;m10&#34;:-398.18096923828125,&#34;m11&#34;:6005.0,&#34;m12&#34;:-1180.909667968750},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;SCREEN&#34;,&#34;visible&#34;:true}" style="mix-blend-mode:screen"/>
</g>
<defs>
<filter id="filter0_d_27_22" x="0" y="0" width="4557" height="3253" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_27_22"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_27_22" result="shape"/>
</filter>
<clipPath id="paint1_angular_27_22_clip_path"><circle cx="2278.5" cy="1622.5" r="3002.5" transform="rotate(-90 2278.5 1622.5)" style="mix-blend-mode:difference"/></clipPath><clipPath id="paint2_angular_27_22_clip_path"><circle cx="2278.5" cy="1622.5" r="3002.5" transform="rotate(-90 2278.5 1622.5)" style="mix-blend-mode:screen"/></clipPath><radialGradient id="paint0_radial_27_22" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1476.53 -1086.6) rotate(82.0073) scale(5767.62)">
<stop stop-color="#2AD0CA"/>
<stop offset="0.229167" stop-color="#E1F664"/>
<stop offset="0.46875" stop-color="#FEB0FE"/>
<stop offset="0.682292" stop-color="#ABB3FC"/>
<stop offset="0.875" stop-color="#5DF7A4"/>
<stop offset="1" stop-color="#58C4F6"/>
</radialGradient>
<clipPath id="clip0_27_22">
<rect width="3245" height="4549" fill="white" transform="matrix(0 -1 1 0 4 3245)"/>
</clipPath>
</defs>
</svg>
