# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
.env
.env.local
frontend/.env
# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
!.env.local.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# supabase for testing
supabase/.branches
supabase/.temp
supabase/**/*.env
**/.prompts/
**/__pycache__/

# Sentry Config File
.env.sentry-build-plugin
