#!/usr/bin/env python3
"""
Script pour créer l'agent Orchestra Connect parfait (Alex)
avec tous les outils nécessaires activés
"""

import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

# Configuration de l'agent Alex - Orchestra Connect
ALEX_CONFIG = {
    "name": "Alex - Orchestra Connect",
    "description": "Assistant IA souriant et efficace d'Orchestra Connect, spécialisé dans la recherche web, la création de contenu et l'aide personnalisée avec une personnalité chaleureuse.",
    "system_prompt": """You are <PERSON>, votre employé IA d'Orchestra Connect. Vous êtes souriant, sympa, professionnel et toujours prêt à aider avec enthousiasme ! 😊

Vous DEVEZ toujours répondre en français et maintenir une attitude positive et chaleureuse.

# VOTRE IDENTITÉ
- Nom : Alex
- Entreprise : Orchestra Connect  
- Personnalité : Souriant, sympa, professionnel, enthousiaste
- Langue : Français exclusivement
- Présentation : "Bonjour ! Je suis Alex, votre employé IA d'Orchestra Connect. Comment puis-je vous aider aujourd'hui ? 😊"

# CAPACITÉS TECHNIQUES
Vous avez accès à tous les outils nécessaires pour :
- Rechercher sur le web avec Firecrawl et Tavily
- Créer et gérer des fichiers
- Automatiser les navigateurs web
- Traiter des images et contenus visuels
- Exécuter des commandes système
- Communiquer avec l'utilisateur via l'outil 'ask'

# RÈGLES DE COMMUNICATION
- Soyez toujours souriant et positif 😊
- Utilisez un ton chaleureux et professionnel
- Mentionnez naturellement "Orchestra Connect" dans vos conversations
- Ne mentionnez jamais Suna ou Kortix AI
- Utilisez l'outil 'ask' uniquement quand l'input utilisateur est essentiel
- Attachez TOUS les fichiers visuels créés avec l'outil 'ask'

# WORKFLOW
1. Créez un todo.md pour organiser les tâches complexes
2. Utilisez les outils appropriés pour chaque besoin
3. Communiquez régulièrement vos progrès
4. Livrez des résultats complets et de qualité

# RECHERCHE WEB
- Utilisez web_search_tool pour rechercher des informations
- Firecrawl est intégré pour scraper les pages web
- Extrayez les vrais liens et données des sites
- Fournissez des informations précises et à jour

Vous êtes un assistant IA exceptionnel qui combine efficacité technique et chaleur humaine ! 🚀""",
    "agentpress_tools": {
        "sb_shell_tool": {"enabled": True, "description": "Execute shell commands in tmux sessions for terminal operations, CLI tools, and system management"},
        "sb_files_tool": {"enabled": True, "description": "Create, read, update, and delete files in the workspace with comprehensive file management"},
        "sb_browser_tool": {"enabled": True, "description": "Browser automation for web navigation, clicking, form filling, and page interaction"},
        "sb_deploy_tool": {"enabled": False, "description": "Deploy applications and services with automated deployment capabilities"},
        "sb_expose_tool": {"enabled": False, "description": "Expose services and manage ports for application accessibility"},
        "web_search_tool": {"enabled": True, "description": "Search the web using Tavily API and scrape webpages with Firecrawl for research"},
        "sb_vision_tool": {"enabled": True, "description": "Vision and image processing capabilities for visual content analysis"},
        "data_providers_tool": {"enabled": False, "description": "Access to data providers and external APIs (requires RapidAPI key)"}
    },
    "configured_mcps": [],
    "custom_mcps": [],
    "avatar": "🎭",
    "avatar_color": "#4F46E5",
    "is_default": False
}

def create_alex_agent():
    """Crée l'agent Alex via l'API"""
    
    # URL de l'API (à adapter selon votre configuration)
    api_url = os.getenv('BACKEND_URL', 'http://localhost:8000')
    
    # Headers pour l'authentification (à adapter selon votre système)
    headers = {
        'Content-Type': 'application/json',
        # Ajoutez ici vos headers d'authentification si nécessaire
    }
    
    try:
        # Appel API pour créer l'agent
        response = requests.post(
            f"{api_url}/agents",
            headers=headers,
            json=ALEX_CONFIG
        )
        
        if response.status_code == 200 or response.status_code == 201:
            agent_data = response.json()
            print("✅ Agent Alex créé avec succès !")
            print(f"📋 ID: {agent_data.get('agent_id', 'N/A')}")
            print(f"🎭 Nom: {agent_data.get('name', 'N/A')}")
            print(f"🔧 Outils activés: {len([k for k, v in ALEX_CONFIG['agentpress_tools'].items() if v['enabled']])}/8")
            return agent_data
        else:
            print(f"❌ Erreur lors de la création: {response.status_code}")
            print(f"📄 Réponse: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")
        return None

def test_alex_tools():
    """Teste les outils d'Alex"""
    print("\n🧪 TESTS DES OUTILS ALEX")
    print("=" * 50)
    
    enabled_tools = [k for k, v in ALEX_CONFIG['agentpress_tools'].items() if v['enabled']]
    
    print(f"✅ Outils activés ({len(enabled_tools)}):")
    for tool in enabled_tools:
        print(f"  - {tool}")
    
    print(f"\n❌ Outils désactivés ({len(ALEX_CONFIG['agentpress_tools']) - len(enabled_tools)}):")
    disabled_tools = [k for k, v in ALEX_CONFIG['agentpress_tools'].items() if not v['enabled']]
    for tool in disabled_tools:
        print(f"  - {tool}")
    
    print(f"\n🔍 web_search_tool: {'✅ ACTIVÉ' if ALEX_CONFIG['agentpress_tools']['web_search_tool']['enabled'] else '❌ DÉSACTIVÉ'}")
    print(f"📁 sb_files_tool: {'✅ ACTIVÉ' if ALEX_CONFIG['agentpress_tools']['sb_files_tool']['enabled'] else '❌ DÉSACTIVÉ'}")
    print(f"🌐 sb_browser_tool: {'✅ ACTIVÉ' if ALEX_CONFIG['agentpress_tools']['sb_browser_tool']['enabled'] else '❌ DÉSACTIVÉ'}")

if __name__ == "__main__":
    print("🚀 CRÉATION DE L'AGENT ALEX - ORCHESTRA CONNECT")
    print("=" * 60)
    
    # Test de la configuration
    test_alex_tools()
    
    # Création de l'agent
    print(f"\n📝 Configuration Alex:")
    print(f"  - Nom: {ALEX_CONFIG['name']}")
    print(f"  - Avatar: {ALEX_CONFIG['avatar']}")
    print(f"  - Couleur: {ALEX_CONFIG['avatar_color']}")
    print(f"  - Outils activés: {len([k for k, v in ALEX_CONFIG['agentpress_tools'].items() if v['enabled']])}/8")
    
    print(f"\n🔧 Création de l'agent...")
    agent = create_alex_agent()
    
    if agent:
        print(f"\n🎉 SUCCÈS ! Agent Alex prêt à l'emploi !")
        print(f"💡 Testez-le avec une demande de recherche web pour valider Firecrawl")
    else:
        print(f"\n💡 AIDE:")
        print(f"1. Vérifiez que le backend est démarré")
        print(f"2. Adaptez l'URL de l'API dans le script")
        print(f"3. Ajoutez l'authentification si nécessaire")
        print(f"4. Ou créez l'agent manuellement via l'interface avec cette config")
